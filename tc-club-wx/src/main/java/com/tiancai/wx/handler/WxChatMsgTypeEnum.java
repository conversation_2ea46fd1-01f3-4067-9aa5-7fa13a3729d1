package com.tiancai.wx.handler;

public enum WxChatMsgTypeEnum {

    SUBSCRIBE("event.subscribe", "用户关注事件"),

    TEXT("text", "接收用户文本消息");

    private String msgtype;

    private String desc;

    WxChatMsgTypeEnum(String msgtype, String desc) {
        this.msgtype = msgtype;
        this.desc = desc;
    }

    public static WxChatMsgTypeEnum getWxChatMsgTypeEnum(String msgtype) {
        for (WxChatMsgTypeEnum wxChatMsgTypeEnum : WxChatMsgTypeEnum.values()) {
            if (wxChatMsgTypeEnum.msgtype.equals(msgtype)) {
                return wxChatMsgTypeEnum;
            }
        }
        return null;
    }
}
