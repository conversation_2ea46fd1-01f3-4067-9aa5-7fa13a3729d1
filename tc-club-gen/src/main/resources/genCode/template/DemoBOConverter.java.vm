package com.jingdianjichi.${module}.domain.convert;

import com.jingdianjichi.${module}.domain.entity.${modelName}BO;
import com.jingdianjichi.${module}.infra.basic.entity.${modelName};
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * ${tableComment} bo转换器
 *
 * <AUTHOR>
 * @since ${genDate}
 */
@Mapper
public interface ${modelName}BOConverter {

    ${modelName}BOConverter INSTANCE = Mappers.getMapper(${modelName}BOConverter.class);

    ${modelName} convertBOToEntity(${modelName}BO ${_modelName}BO);

}
