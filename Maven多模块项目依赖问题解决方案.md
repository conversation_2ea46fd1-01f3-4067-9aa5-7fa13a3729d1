# Maven多模块项目依赖问题解决方案

## 项目概述

本文档记录了 tc-club 项目中遇到的 Maven 多模块依赖问题及其完整解决方案。项目包含两个主要模块：
- `tc-club-auth`：认证模块
- `tc-club-subject`：题目管理模块

## 问题分类与解决方案

### 1. 循环依赖问题

#### 问题描述
模块之间存在循环依赖，导致编译失败：
```
tc-club-auth-domain ↔ tc-club-auth-infra
```

#### 解决方案
**移除不必要的依赖关系**：
```xml
<!-- 在 tc-club-auth-infra/pom.xml 中移除对 tc-club-auth-domain 的依赖 -->
<dependency>
    <groupId>com.tiancai</groupId>
    <artifactId>tc-club-auth-domain</artifactId>
    <version>1.0-SNAPSHOT</version>
</dependency>
```

#### 最佳实践
- 遵循分层架构：infra → domain → application
- 避免下层模块依赖上层模块
- 使用接口和抽象类解耦

### 2. 依赖管理配置错误

#### 问题描述
父 pom.xml 中错误使用 `<dependencies>` 标签，应该使用 `<dependencyManagement>`：

```xml
<!-- 错误配置 -->
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>2.4.2</version>
        <type>pom</type>
        <scope>import</scope>
    </dependency>
</dependencies>
```

#### 解决方案
```xml
<!-- 正确配置 -->
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-dependencies</artifactId>
            <version>2.4.2</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>
```

### 3. 重复模块声明

#### 问题描述
父 pom.xml 中存在重复的子模块声明：
```xml
<modules>
    <module>tc-club-auth-api</module>
    <module>tc-club-auth-api</module>  <!-- 重复 -->
</modules>
```

#### 解决方案
移除重复的模块声明，确保每个模块只声明一次。

### 4. 编码问题

#### 问题描述
编译时出现中文乱码：
```
[WARNING] File encoding has not been set, using platform encoding GBK
```

#### 解决方案
在所有 pom.xml 中添加编码配置：
```xml
<properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
</properties>
```

### 5. Lombok 版本兼容性问题

#### 问题描述
使用旧版本 Lombok (1.18.16) 在 Java 17 环境下出现模块访问错误：
```
java.lang.IllegalAccessError: class lombok.javac.apt.LombokProcessor cannot access class com.sun.tools.javac.processing.JavacProcessingEnvironment
```

#### 解决方案

**5.1 升级 Lombok 版本**
```xml
<dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
    <version>1.18.24</version>
    <scope>provided</scope>
</dependency>
```

**5.2 配置编译器插件**

对于使用 Lombok 的模块：
```xml
<build>
    <plugins>
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>3.6.1</version>
            <configuration>
                <source>8</source>
                <target>8</target>
                <annotationProcessorPaths>
                    <path>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                        <version>1.18.24</version>
                    </path>
                    <path>
                        <groupId>org.mapstruct</groupId>
                        <artifactId>mapstruct-processor</artifactId>
                        <version>1.4.2.Final</version>
                    </path>
                </annotationProcessorPaths>
            </configuration>
        </plugin>
    </plugins>
</build>
```

对于不使用 Lombok 的模块：
```xml
<build>
    <plugins>
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>3.6.1</version>
            <configuration>
                <source>8</source>
                <target>8</target>
                <proc>none</proc>
            </configuration>
        </plugin>
    </plugins>
</build>
```

## 通用解决方案教程

### 步骤1：分析依赖关系

使用 Maven 命令分析依赖：
```bash
# 查看依赖树
mvn dependency:tree

# 分析依赖冲突
mvn dependency:analyze
```

### 步骤2：修复循环依赖

1. **识别循环依赖**：使用依赖分析工具或手动检查
2. **重构架构**：按照分层原则重新组织模块
3. **移除不必要依赖**：删除造成循环的依赖关系
4. **使用接口解耦**：通过接口和抽象类减少直接依赖

### 步骤3：统一依赖管理

1. **创建父 pom**：统一管理所有依赖版本
2. **使用 dependencyManagement**：在父 pom 中声明依赖版本
3. **子模块继承**：子模块只声明 groupId 和 artifactId

### 步骤4：配置编译器插件

1. **统一编译器版本**：所有模块使用相同的 maven-compiler-plugin 版本
2. **配置 Lombok**：根据模块是否使用 Lombok 进行不同配置
3. **设置编码**：统一使用 UTF-8 编码

### 步骤5：逐步编译验证

```bash
# 1. 编译基础模块
mvn clean compile -f tc-club-auth/tc-club-auth-common/pom.xml

# 2. 安装到本地仓库
mvn install -f tc-club-auth/tc-club-auth-common/pom.xml

# 3. 编译依赖模块
mvn clean compile -f tc-club-auth/tc-club-auth-infra/pom.xml

# 4. 编译整个项目
mvn clean compile -f tc-club-auth/pom.xml
```

## 预防措施

### 1. 项目结构规范
```
project-root/
├── pom.xml (父 pom)
├── module-common/
├── module-infra/
├── module-domain/
├── module-application/
└── module-starter/
```

### 2. 依赖原则
- **单向依赖**：避免循环依赖
- **最小依赖**：只引入必要的依赖
- **版本统一**：通过父 pom 管理版本

### 3. 编码规范
- 统一使用 UTF-8 编码
- 统一 Java 版本
- 统一 Maven 插件版本

## 常见错误及解决方案

### 错误1：找不到依赖
```
Could not resolve dependencies for project com.tiancai:tc-club-infra:jar:1.0-SNAPSHOT
```
**解决方案**：先编译并安装被依赖的模块到本地仓库

### 错误2：Lombok 编译错误
```
cannot find symbol: method getId()
```
**解决方案**：检查 Lombok 配置，确保注解处理器正确配置

### 错误3：编码问题
```
unmappable character for encoding GBK
```
**解决方案**：添加 UTF-8 编码配置

## 总结

通过系统性地解决依赖管理、循环依赖、Lombok 配置等问题，可以确保 Maven 多模块项目的稳定编译和运行。关键是要遵循良好的架构设计原则，统一配置管理，并采用逐步验证的方法来解决问题。
