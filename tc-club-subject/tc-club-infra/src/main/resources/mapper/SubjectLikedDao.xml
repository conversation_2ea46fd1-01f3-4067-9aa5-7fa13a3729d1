<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiancai.subject.infra.basic.mapper.SubjectLikedDao">

    <resultMap id="BaseResultMap" type="com.tiancai.subject.infra.basic.entity.SubjectLiked">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="subject_id" jdbcType="BIGINT" property="subjectId"/>
        <result column="like_user_id" jdbcType="VARCHAR" property="likeUserId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
    </resultMap>

    <insert id="insertBatch">
        insert into subject_liked(id, subject_id, like_user_id, status, created_by, created_time, update_by,
        update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},
            #{entity.subjectId},
            #{entity.likeUserId},
            #{entity.status},
            #{entity.createdBy},
            #{entity.createdTime},
            #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
    </insert>

    <select id="countByCondition" resultType="java.lang.Integer">
        select count(1)
        from subject_liked
        where like_user_id = #{likeUserId} and status = 1
          and is_deleted = 0
    </select>

    <select id="queryPage" resultType="com.tiancai.subject.infra.basic.entity.SubjectLiked">
        select *
        from subject_liked
        where like_user_id = #{entity.likeUserId}
          and is_deleted = 0 and status = 1
            limit #{start}
            , #{pageSize}
    </select>

    <insert id="batchInsertOrUpdate">
        INSERT INTO subject_liked
        (subject_id, like_user_id, status, created_by, created_time, update_by, update_time, is_deleted)
        VALUES
        <foreach collection="entities" item="item" separator=",">
            (#{item.subjectId}, #{item.likeUserId}, #{item.status}, #{item.createdBy}, #{item.createdTime}, #{item.updateBy}, #{item.updateTime}, #{item.isDeleted})
        </foreach>
        ON DUPLICATE KEY UPDATE
        status = VALUES(status),
        created_by = VALUES(created_by),
        created_time = VALUES(created_time),
        update_by = VALUES(update_by),
        update_time = VALUES(update_time),
        is_deleted = VALUES(is_deleted)
    </insert>

</mapper>
