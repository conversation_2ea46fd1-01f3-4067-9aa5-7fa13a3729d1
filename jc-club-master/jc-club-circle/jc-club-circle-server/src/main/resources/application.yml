server:
  port: 3014
spring:
  datasource:
    username: root
    password: qvQP7MBvSkbyGzLzlRaPp9swmOmkqdVkVgBNPQF7pMlImathGYopQcWR2CuZMZAkL1xrDHwut9Hbr2TZ4qmr2Q==
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 20
      min-idle: 20
      connectionProperties: config.decrypt=true;config.decrypt.key=${publicKey};
      max-active: 100
      max-wait: 60000
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: 123456
      filter:
        stat:
          enabled: true
          slow-sql-millis: 2000
          log-slow-sql: true
        wall:
          enabled: true
        config:
          enabled: true
  redis:
    # Redis数据库索引（默认为0）
    database: 1
    # Redis服务器地址
    host: *************
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    password: jichi1234
    # 连接超时时间
    timeout: 2s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMJzo9TiSuOGAMR2Zma25lWdtR1oxq6RcZYnWE9vcYLNKxUOkBlvSfMrbS25KtlJi+hIzikfCoyTDB0VI5gB3Q8CAwEAAQ==
logging:
  config: classpath:log4j2-spring.xml