<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingdianjichi.circle.server.dao.ShareMomentMapper">

    <update id="incrReplyCount">
        update share_moment
        set reply_count = reply_count + #{count}
        where id = #{id}
          and is_deleted = 0
    </update>

</mapper>
