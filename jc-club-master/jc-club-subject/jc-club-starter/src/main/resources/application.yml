server:
  port: 3010
spring:
  datasource:
    username: root
    password: qvQP7MBvSkbyGzLzlRaPp9swmOmkqdVkVgBNPQF7pMlImathGYopQcWR2CuZMZAkL1xrDHwut9Hbr2TZ4qmr2Q==
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 20
      min-idle: 20
      connectionProperties: config.decrypt=true;config.decrypt.key=${publicKey};
      max-active: 100
      max-wait: 60000
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: 123456
      filter:
        stat:
          enabled: true
          slow-sql-millis: 2000
          log-slow-sql: true
        wall:
          enabled: true
        config:
          enabled: true
  redis:
    # Redis数据库索引（默认为0）
    database: 0
    # Redis服务器地址
    host: *************
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    password: jichi1234
    # 连接超时时间
    timeout: 2s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
logging:
  config: classpath:log4j2-spring.xml
es:
  cluster:
    esConfigs[0]:
      name: 73438a827b55
      nodes: *************:9200
xxl:
  job:
    admin:
      addresses: http://127.0.0.1:8080/xxl-job-admin
    accessToken: default_token
    executor:
      appname: jc-club-subjcet
      address:
      ip: 127.0.0.1
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
rocketmq:
  name-server: ************:9876
  producer:
    group: test-group
