<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingdianjichi.practice.server.dao.SubjectMultipleDao">

    <select id="selectBySubjectId"
            resultType="com.jingdianjichi.practice.server.entity.po.SubjectMultiplePO">
        select option_type as optionType, option_content as optionContent, is_correct as isCorrect
        from subject_multiple
        where is_deleted = 0
          and subject_id = #{subjectId,jdbcType=BIGINT}
        order by option_type
    </select>

</mapper>
