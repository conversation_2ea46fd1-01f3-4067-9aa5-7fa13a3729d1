<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingdianjichi.interview.server.dao.InterviewHistoryDao">

    <resultMap type="com.jingdianjichi.interview.server.entity.po.InterviewHistory" id="InterviewHistoryMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="avgScore" column="avg_score" jdbcType="DOUBLE"/>
        <result property="keyWords" column="key_words" jdbcType="VARCHAR"/>
        <result property="tip" column="tip" jdbcType="VARCHAR"/>
        <result property="interviewUrl" column="interview_url" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="InterviewHistoryMap">
        select id,
               avg_score,
               key_words,
               tip,
               interview_url,
               created_by,
               created_time,
               update_by,
               update_time,
               is_deleted
        from interview_history
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="InterviewHistoryMap">
        select
        id, avg_score, key_words, tip, interview_url, created_by, created_time, update_by, update_time, is_deleted
        from interview_history
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="avgScore != null and avgScore != ''">
                and avg_score = #{avgScore}
            </if>
            <if test="keyWords != null and keyWords != ''">
                and key_words = #{keyWords}
            </if>
            <if test="tip != null and tip != ''">
                and tip = #{tip}
            </if>
            <if test="interviewUrl != null and interviewUrl != ''">
                and interview_url = #{interviewUrl}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from interview_history
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="avgScore != null and avgScore != ''">
                and avg_score = #{avgScore}
            </if>
            <if test="keyWords != null and keyWords != ''">
                and key_words = #{keyWords}
            </if>
            <if test="tip != null and tip != ''">
                and tip = #{tip}
            </if>
            <if test="interviewUrl != null and interviewUrl != ''">
                and interview_url = #{interviewUrl}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into interview_history(avg_score, key_words, tip, interview_url, created_by, created_time, update_by,
                                      update_time, is_deleted)
        values (#{avgScore}, #{keyWords}, #{tip}, #{interviewUrl}, #{createdBy}, #{createdTime}, #{updateBy},
                #{updateTime}, #{isDeleted})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into interview_history(avg_score, key_words, tip, interview_url, created_by, created_time, update_by,
        update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.avgScore}, #{entity.keyWords}, #{entity.tip}, #{entity.interviewUrl}, #{entity.createdBy},
            #{entity.createdTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into interview_history(avg_score, key_words, tip, interview_url, created_by, created_time, update_by,
        update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.avgScore}, #{entity.keyWords}, #{entity.tip}, #{entity.interviewUrl}, #{entity.createdBy},
            #{entity.createdTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
        on duplicate key update
        avg_score = values(avg_score),
        key_words = values(key_words),
        tip = values(tip),
        interview_url = values(interview_url),
        created_by = values(created_by),
        created_time = values(created_time),
        update_by = values(update_by),
        update_time = values(update_time),
        is_deleted = values(is_deleted)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update interview_history
        <set>
            <if test="avgScore != null and avgScore != ''">
                avg_score = #{avgScore},
            </if>
            <if test="keyWords != null and keyWords != ''">
                key_words = #{keyWords},
            </if>
            <if test="tip != null and tip != ''">
                tip = #{tip},
            </if>
            <if test="interviewUrl != null and interviewUrl != ''">
                interview_url = #{interviewUrl},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from interview_history
        where id = #{id}
    </delete>

</mapper>

