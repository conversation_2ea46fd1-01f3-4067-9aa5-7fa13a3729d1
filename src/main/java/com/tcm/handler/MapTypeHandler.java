package com.tcm.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 优化版的 JSON 类型处理器，专门用于处理 Map<String, String> 类型
 * 增加了更多的错误处理和日志
 */
@MappedTypes(Map.class)
public class MapTypeHandler extends BaseTypeHandler<Map<String, String>> {

    private static final Logger logger = LoggerFactory.getLogger(MapTypeHandler.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Map<String, String> parameter, JdbcType jdbcType) throws SQLException {
        try {
            String json = objectMapper.writeValueAsString(parameter);
            logger.debug("Serializing Map to JSON: {}", json);
            ps.setString(i, json);
        } catch (JsonProcessingException e) {
            logger.error("Error serializing Map to JSON", e);
            throw new SQLException("Error serializing Map to JSON", e);
        }
    }

    @Override
    public Map<String, String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseJson(rs.getString(columnName));
    }

    @Override
    public Map<String, String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseJson(rs.getString(columnIndex));
    }

    @Override
    public Map<String, String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseJson(cs.getString(columnIndex));
    }

    private Map<String, String> parseJson(String json) {
        if (json == null || json.isEmpty()) {
            return Collections.emptyMap();
        }
        
        try {
            logger.debug("Deserializing JSON to Map: {}", json);
            // 使用TypeReference确保正确的泛型类型
            Map<String, String> result = objectMapper.readValue(json, 
                    new TypeReference<Map<String, String>>() {});
            return result != null ? result : Collections.emptyMap();
        } catch (Exception e) {
            logger.error("Error parsing JSON: {}", json, e);
            // 出错时返回空Map而不是null，避免前端出错
            return Collections.emptyMap();
        }
    }
} 