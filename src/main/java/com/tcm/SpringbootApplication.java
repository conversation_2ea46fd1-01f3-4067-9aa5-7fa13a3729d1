package com.tcm;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 应用启动类
 */
@SpringBootApplication
@MapperScan("com.tcm.mapper")
@EnableScheduling
@Slf4j
public class SpringbootApplication {

    public static void main(String[] args) {
        log.info("===== 应用启动中，正在初始化... =====");
        long startTime = System.currentTimeMillis();
        
        try {
            SpringApplication.run(SpringbootApplication.class, args);
            log.info("===== 应用启动完成，耗时: {} ms =====", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("===== 应用启动失败 =====", e);
            System.exit(1);
        }
    }

}
