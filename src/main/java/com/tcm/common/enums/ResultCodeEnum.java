package com.tcm.common.enums;

public enum ResultCodeEnum {
    SUCCESS("200", "成功"),

    PARAM_ERROR("400", "参数异常"),
    TOKEN_INVALID_ERROR("401", "无效的token"),
    TOKEN_CHECK_ERROR("401", "token验证失败，请重新登录"),
    PARAM_LOST_ERROR("4001", "参数缺失"),

    SYSTEM_ERROR("500", "系统异常"),
    USER_EXIST_ERROR("5001", "用户名已存在"),
    USER_NOT_LOGIN("5002", "用户未登录"),
    USER_ACCOUNT_ERROR("5003", "账号或密码错误"),
    USER_NOT_EXIST_ERROR("5004", "用户不存在"),
    PARAM_PASSWORD_ERROR("5005", "原密码输入错误"),
    RECOMMEND_ALREADY_ERROR("5006", "已经推荐过了，请先下架"),
    SIGNIN_ALREADY_ERROR("5007", "今天已经签到过了，明天再来吧！"),
    ACCOUNT_LOWER_ERROR("5008", "账户余额不足，请进行充值！"),
    SCORE_LOWER_ERROR("5009", "积分不足"),
    USER_NOT_LOGGED_IN("5010", "用户身份验证失败"),
    LOGIN_ERROR("5011", "登录异常"),
    TEST_NOT_FOUND("5012", "试题不存在"),
    FILE_EMPTY_ERROR("5013", "上传文件为空"),
    FILE_FORMAT_ERROR("5014", "文件格式不正确"),
    IMPORT_ERROR("5015", "导入失败"),
    NO_VALID_DATA_ERROR("5016", "没有有效数据");
    public String code;
    public String msg;

    ResultCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
