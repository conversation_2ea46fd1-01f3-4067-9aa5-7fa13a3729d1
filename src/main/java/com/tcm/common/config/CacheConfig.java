package com.tcm.common.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 多级缓存配置
 * L1: Caffeine本地缓存 (快速访问)
 * L2: Redis分布式缓存 (共享数据)
 */
@Slf4j
@Configuration
@EnableCaching
public class CacheConfig {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    /**
     * Caffeine本地缓存管理器 (L1缓存)
     */
    @Bean("caffeineCacheManager")
    @Primary
    public CacheManager caffeineCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(1000)                    // 最大缓存条目数
                .expireAfterWrite(5, TimeUnit.MINUTES) // 写入后5分钟过期
                .expireAfterAccess(2, TimeUnit.MINUTES) // 访问后2分钟过期
                .recordStats());                       // 启用统计
        
        log.info("Caffeine本地缓存管理器初始化完成");
        return cacheManager;
    }

    /**
     * Redis分布式缓存管理器 (L2缓存)
     */
    @Bean("redisCacheManager")
    public CacheManager redisCacheManager() {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))  // 默认30分钟过期
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues();       // 不缓存null值

        RedisCacheManager cacheManager = RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(config)
                .build();
        
        log.info("Redis分布式缓存管理器初始化完成");
        return cacheManager;
    }

    /**
     * 统计数据专用缓存 (短期缓存)
     */
    @Bean("statsCache")
    public Cache<String, Object> statsCache() {
        return Caffeine.newBuilder()
                .maximumSize(100)
                .expireAfterWrite(1, TimeUnit.MINUTES)  // 统计数据1分钟过期
                .recordStats()
                .build();
    }

    /**
     * 用户信息缓存 (中期缓存)
     */
    @Bean("userCache")
    public Cache<String, Object> userCache() {
        return Caffeine.newBuilder()
                .maximumSize(500)
                .expireAfterWrite(10, TimeUnit.MINUTES) // 用户信息10分钟过期
                .expireAfterAccess(5, TimeUnit.MINUTES)  // 访问后5分钟过期
                .recordStats()
                .build();
    }

    /**
     * 题目数据缓存 (长期缓存)
     */
    @Bean("questionCache")
    public Cache<String, Object> questionCache() {
        return Caffeine.newBuilder()
                .maximumSize(2000)
                .expireAfterWrite(1, TimeUnit.HOURS)    // 题目数据1小时过期
                .expireAfterAccess(30, TimeUnit.MINUTES) // 访问后30分钟过期
                .recordStats()
                .build();
    }

    /**
     * 考试配置缓存 (长期缓存)
     */
    @Bean("examCache")
    public Cache<String, Object> examCache() {
        return Caffeine.newBuilder()
                .maximumSize(200)
                .expireAfterWrite(2, TimeUnit.HOURS)    // 考试配置2小时过期
                .expireAfterAccess(1, TimeUnit.HOURS)   // 访问后1小时过期
                .recordStats()
                .build();
    }
}
