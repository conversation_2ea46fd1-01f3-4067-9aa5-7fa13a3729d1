package com.tcm.common.config;

import cn.dev33.satoken.stp.StpUtil;
import com.tcm.common.enums.RoleEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 权限策略配置
 * 定义更灵活的权限控制策略
 */
@Slf4j
@Component
public class PermissionStrategy {

    /**
     * 检查用户是否有访问权限
     * 超级管理员拥有所有权限，管理员拥有大部分权限，普通用户有基础权限
     */
    public boolean hasAccess(String resource) {
        try {
            if (!StpUtil.isLogin()) {
                return false;
            }

            // 获取用户角色
            String role = getCurrentUserRole();

            // 超级管理员拥有所有权限
            if ("SUPER_ADMIN".equals(role)) {
                log.debug("超级管理员用户 {} 访问资源: {}", StpUtil.getLoginId(), resource);
                return true;
            }

            // 管理员拥有所有权限
            if (RoleEnum.ADMIN.name().equals(role)) {
                log.debug("管理员用户 {} 访问资源: {}", StpUtil.getLoginId(), resource);
                return true;
            }

            // 教师角色权限检查
            if ("TEACHER".equals(role)) {
                return hasTeacherPermission(resource);
            }

            // 普通用户的权限检查
            if (RoleEnum.USER.name().equals(role) || "STUDENT".equals(role)) {
                return hasUserPermission(resource);
            }

            return false;
        } catch (Exception e) {
            log.error("权限检查失败", e);
            return false;
        }
    }

    /**
     * 检查教师权限
     */
    private boolean hasTeacherPermission(String resource) {
        // 教师可以访问的资源（比普通用户多一些管理功能）
        String[] allowedResources = {
            "course", "exam", "question", "dashboard", "student",
            "user/profile", "password", "file", "notice", "information",
            "comment", "score", "test", "answerRecord"
        };

        for (String allowed : allowedResources) {
            if (resource.contains(allowed)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查普通用户权限
     */
    private boolean hasUserPermission(String resource) {
        // 普通用户可以访问的资源
        String[] allowedResources = {
            "course", "exam", "question", "dashboard",
            "user/profile", "password", "file/download"
        };

        for (String allowed : allowedResources) {
            if (resource.contains(allowed)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取当前用户角色
     */
    private String getCurrentUserRole() {
        try {
            // 从Sa-Token的Session中获取角色信息
            Object roleObj = StpUtil.getSession().get("userInfo");
            if (roleObj instanceof java.util.Map) {
                @SuppressWarnings("unchecked")
                java.util.Map<String, Object> userInfo = (java.util.Map<String, Object>) roleObj;
                return (String) userInfo.get("role");
            }
            
            // 如果Session中没有，尝试从登录ID解析
            String loginId = StpUtil.getLoginId().toString();
            if (loginId.contains("-")) {
                String[] parts = loginId.split("-");
                if (parts.length == 2) {
                    return parts[1];
                }
            }
            
            return null;
        } catch (Exception e) {
            log.error("获取用户角色失败", e);
            return null;
        }
    }

    /**
     * 检查是否为管理员（包括超级管理员）
     */
    public boolean isAdmin() {
        try {
            if (!StpUtil.isLogin()) {
                return false;
            }

            String role = getCurrentUserRole();
            return "SUPER_ADMIN".equals(role) || RoleEnum.ADMIN.name().equals(role);
        } catch (Exception e) {
            log.error("检查管理员权限失败", e);
            return false;
        }
    }

    /**
     * 检查是否为超级管理员
     */
    public boolean isSuperAdmin() {
        try {
            if (!StpUtil.isLogin()) {
                return false;
            }

            String role = getCurrentUserRole();
            return "SUPER_ADMIN".equals(role);
        } catch (Exception e) {
            log.error("检查超级管理员权限失败", e);
            return false;
        }
    }

    /**
     * 检查是否为普通用户
     */
    public boolean isUser() {
        try {
            if (!StpUtil.isLogin()) {
                return false;
            }
            
            String role = getCurrentUserRole();
            return RoleEnum.USER.name().equals(role);
        } catch (Exception e) {
            log.error("检查用户权限失败", e);
            return false;
        }
    }
}
