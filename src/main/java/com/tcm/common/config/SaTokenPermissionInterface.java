package com.tcm.common.config;

import cn.dev33.satoken.stp.StpInterface;
import com.tcm.entity.Account;
import com.tcm.entity.Permission;
import com.tcm.entity.Role;
import com.tcm.service.PermissionService;
import com.tcm.service.SaTokenAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Sa-Token权限数据接口实现
 * 基于数据库的动态权限验证接口
 */
@Slf4j
@Component
public class SaTokenPermissionInterface implements StpInterface {

    @Autowired
    private SaTokenAuthService saTokenAuthService;

    @Autowired
    private PermissionService permissionService;

    /**
     * 返回一个账号所拥有的权限码集合
     * 从数据库动态获取用户权限
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        List<String> permissions = new ArrayList<>();

        try {
            Account currentUser = saTokenAuthService.getCurrentUser();
            if (currentUser == null || currentUser.getId() == null) {
                log.debug("用户未登录或用户信息不完整，loginId: {}", loginId);
                return permissions;
            }

            // 确定用户类型
            String userType = determineUserType(currentUser.getRole());

            // 从数据库获取用户权限
            List<Permission> userPermissions = permissionService.getUserPermissions(
                currentUser.getId(), userType);

            // 转换为权限编码列表
            permissions = userPermissions.stream()
                    .map(Permission::getPermissionCode)
                    .collect(Collectors.toList());

            log.debug("用户 {} (ID: {}, Type: {}) 的权限列表: {}",
                     loginId, currentUser.getId(), userType, permissions);

        } catch (Exception e) {
            log.error("获取用户权限失败，loginId: {}", loginId, e);
        }

        return permissions;
    }

    /**
     * 返回一个账号所拥有的角色标识集合
     * 从数据库动态获取用户角色
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        List<String> roles = new ArrayList<>();

        try {
            Account currentUser = saTokenAuthService.getCurrentUser();
            if (currentUser == null || currentUser.getId() == null) {
                log.debug("用户未登录或用户信息不完整，loginId: {}", loginId);
                return roles;
            }

            // 确定用户类型
            String userType = determineUserType(currentUser.getRole());

            // 从数据库获取用户角色
            List<Role> userRoles = permissionService.getUserRoles(
                currentUser.getId(), userType);

            // 转换为角色编码列表
            roles = userRoles.stream()
                    .map(Role::getRoleCode)
                    .collect(Collectors.toList());

            // 保持向后兼容，添加原有的角色标识
            if (currentUser.getRole() != null && !roles.contains(currentUser.getRole())) {
                roles.add(currentUser.getRole());
            }

            log.debug("用户 {} (ID: {}, Type: {}) 的角色列表: {}",
                     loginId, currentUser.getId(), userType, roles);

        } catch (Exception e) {
            log.error("获取用户角色失败，loginId: {}", loginId, e);
        }

        return roles;
    }

    /**
     * 根据角色确定用户类型
     */
    private String determineUserType(String role) {
        if ("SUPER_ADMIN".equals(role) || "TEACHER".equals(role)) {
            return "admin";
        } else if ("STUDENT".equals(role)) {
            return "student";
        } else {
            // 兼容原有的ADMIN和USER角色
            return "ADMIN".equals(role) ? "admin" : "student";
        }
    }
}
