package com.tcm.common.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token配置类
 * 简化版本，专注核心功能
 */
@Slf4j
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    /**
     * 注册Sa-Token拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token拦截器，采用简单的认证规则
        registry.addInterceptor(new SaInterceptor(handler -> {
            
            // 公开接口，无需登录
            SaRouter.match("/**")
                .notMatch("/", "/login", "/register")  // 基础认证接口
                .notMatch("/doc.html", "/v2/api-docs", "/swagger-resources/**", "/swagger-ui/**", "/webjars/**")  // API文档
                .notMatch("/files/**", "/actuator/**")  // 文件和监控接口
                .notMatch("/performance/**")  // 性能监控接口
                .notMatch("/error", "/favicon.ico")  // 系统接口
                .check(r -> {
                    // 只检查登录状态
                    StpUtil.checkLogin();
                    log.debug("Sa-Token登录验证通过，用户ID: {}", StpUtil.getLoginId());
                });

        })).addPathPatterns("/**");
        
        log.info("Sa-Token拦截器注册完成");
    }
}
