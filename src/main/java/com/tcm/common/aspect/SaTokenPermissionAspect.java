package com.tcm.common.aspect;

import cn.dev33.satoken.stp.StpUtil;
import com.tcm.common.annotation.SaCheckPermission;
import com.tcm.common.annotation.SaCheckRole;
import com.tcm.common.enums.ResultCodeEnum;
import com.tcm.exception.CustomException;
import com.tcm.service.SaTokenAuthService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * Sa-Token权限校验切面
 */
@Slf4j
@Aspect
@Component
@Order(1) // 确保在其他切面之前执行
public class SaTokenPermissionAspect {

    @Autowired
    private SaTokenAuthService saTokenAuthService;

    /**
     * 角色权限校验
     */
    @Around("@annotation(saCheckRole)")
    public Object checkRole(ProceedingJoinPoint joinPoint, SaCheckRole saCheckRole) throws Throwable {
        try {
            // 检查是否登录
            if (!StpUtil.isLogin()) {
                throw new CustomException(ResultCodeEnum.USER_NOT_LOGIN);
            }

            String[] roles = saCheckRole.value();
            String mode = saCheckRole.mode();

            if (roles.length == 0) {
                // 没有指定角色，只检查登录状态
                return joinPoint.proceed();
            }

            boolean hasRole = false;
            
            if ("OR".equals(mode)) {
                // OR模式：只要有其中一个角色即可
                for (String role : roles) {
                    if (saTokenAuthService.hasRole(role)) {
                        hasRole = true;
                        break;
                    }
                }
            } else {
                // AND模式：必须拥有所有角色
                hasRole = true;
                for (String role : roles) {
                    if (!saTokenAuthService.hasRole(role)) {
                        hasRole = false;
                        break;
                    }
                }
            }

            if (!hasRole) {
                log.warn("用户权限不足，需要角色: {}, 当前用户: {}", 
                    String.join(",", roles), StpUtil.getLoginId());
                throw new CustomException(ResultCodeEnum.USER_NOT_LOGIN);
            }

            return joinPoint.proceed();

        } catch (CustomException e) {
            throw e;
        } catch (Exception e) {
            log.error("角色权限校验失败", e);
            throw new CustomException(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 权限校验
     */
    @Around("@annotation(saCheckPermission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint, SaCheckPermission saCheckPermission) throws Throwable {
        try {
            // 检查是否登录
            if (!StpUtil.isLogin()) {
                throw new CustomException(ResultCodeEnum.USER_NOT_LOGIN);
            }

            String[] permissions = saCheckPermission.value();
            String mode = saCheckPermission.mode();

            if (permissions.length == 0) {
                // 没有指定权限，只检查登录状态
                return joinPoint.proceed();
            }

            boolean hasPermission = false;
            
            if ("OR".equals(mode)) {
                // OR模式：只要有其中一个权限即可
                for (String permission : permissions) {
                    if (saTokenAuthService.hasPermission(permission)) {
                        hasPermission = true;
                        break;
                    }
                }
            } else {
                // AND模式：必须拥有所有权限
                hasPermission = true;
                for (String permission : permissions) {
                    if (!saTokenAuthService.hasPermission(permission)) {
                        hasPermission = false;
                        break;
                    }
                }
            }

            if (!hasPermission) {
                log.warn("用户权限不足，需要权限: {}, 当前用户: {}", 
                    String.join(",", permissions), StpUtil.getLoginId());
                throw new CustomException(ResultCodeEnum.USER_NOT_LOGIN);
            }

            return joinPoint.proceed();

        } catch (CustomException e) {
            throw e;
        } catch (Exception e) {
            log.error("权限校验失败", e);
            throw new CustomException(ResultCodeEnum.SYSTEM_ERROR);
        }
    }
}
