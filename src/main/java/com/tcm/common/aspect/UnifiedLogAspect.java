package com.tcm.common.aspect;

import com.tcm.service.AsyncLogService;
import com.tcm.service.PerformanceMetricsService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * 统一日志切面
 * 整合请求日志、性能监控、异常记录等功能，简化日志输出
 */
@Aspect
@Component
@Slf4j
public class UnifiedLogAspect {

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private PerformanceMetricsService performanceMetricsService;

    // 性能监控阈值配置
    private static final long SLOW_METHOD_THRESHOLD = 1000L; // 1秒
    private static final long LOG_METHOD_THRESHOLD = 100L;   // 100毫秒

    /**
     * Controller层切点
     */
    @Pointcut("execution(* com.tcm.controller..*(..))")
    public void controllerPointcut() {
    }

    /**
     * Service层切点
     */
    @Pointcut("execution(* com.tcm.service.impl..*(..))")
    public void servicePointcut() {
    }

    /**
     * Controller层统一处理：请求日志 + 性能监控
     */
    @Around("controllerPointcut()")
    public Object handleController(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String methodName = getMethodName(joinPoint);
        
        // 简化的请求日志记录
        logRequestInfo(joinPoint);
        
        try {
            Object result = joinPoint.proceed();
            
            // 记录执行时间和性能指标
            long executionTime = System.currentTimeMillis() - startTime;
            logPerformance(methodName, executionTime, joinPoint.getArgs(), "Controller");

            // 记录API调用指标
            try {
                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                if (attributes != null) {
                    HttpServletRequest request = attributes.getRequest();
                    performanceMetricsService.recordApiCall(
                        request.getRequestURI(),
                        request.getMethod(),
                        200, // 成功状态码
                        executionTime
                    );
                }
            } catch (Exception e) {
                log.debug("记录API指标失败", e);
            }

            return result;
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 异步记录异常和性能信息
            asyncLogService.logSystemError(
                "Controller异常", 
                e.getMessage(), 
                getSimpleStackTrace(e), 
                null
            );
            
            asyncLogService.logPerformance(methodName + "(异常)", executionTime, Arrays.toString(joinPoint.getArgs()));
            
            throw e;
        }
    }

    /**
     * Service层性能监控
     */
    @Around("servicePointcut()")
    public Object handleService(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String methodName = getMethodName(joinPoint);
        
        try {
            Object result = joinPoint.proceed();
            
            // 只记录慢方法
            long executionTime = System.currentTimeMillis() - startTime;
            if (executionTime > LOG_METHOD_THRESHOLD) {
                logPerformance(methodName, executionTime, joinPoint.getArgs(), "Service");
            }
            
            return result;
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 异步记录异常
            asyncLogService.logSystemError(
                "Service异常", 
                e.getMessage(), 
                getSimpleStackTrace(e), 
                null
            );
            
            if (executionTime > LOG_METHOD_THRESHOLD) {
                asyncLogService.logPerformance(methodName + "(异常)", executionTime, Arrays.toString(joinPoint.getArgs()));
            }
            
            throw e;
        }
    }

    /**
     * 记录请求信息（简化版）
     */
    private void logRequestInfo(ProceedingJoinPoint joinPoint) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String methodName = getMethodName(joinPoint);
                
                // 简化的请求日志，只记录关键信息
                log.info("API调用: {} {} - {}", 
                    request.getMethod(), 
                    request.getRequestURI(), 
                    methodName);
            }
        } catch (Exception e) {
            // 日志记录失败不影响业务
            log.debug("记录请求信息失败", e);
        }
    }

    /**
     * 记录性能信息
     */
    private void logPerformance(String methodName, long executionTime, Object[] args, String layer) {
        if (executionTime > SLOW_METHOD_THRESHOLD) {
            // 慢方法记录警告日志
            log.warn("慢方法检测: {} 执行时间: {}ms", methodName, executionTime);
            
            // 异步记录详细性能信息
            asyncLogService.logPerformance(methodName, executionTime, Arrays.toString(args));
        } else if (executionTime > LOG_METHOD_THRESHOLD) {
            // 普通慢方法记录调试日志
            log.debug("{} 方法: {} 执行时间: {}ms", layer, methodName, executionTime);
        }
    }

    /**
     * 获取方法全名
     */
    private String getMethodName(ProceedingJoinPoint joinPoint) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        return className + "." + methodName;
    }

    /**
     * 获取简化的堆栈跟踪
     */
    private String getSimpleStackTrace(Exception e) {
        StringBuilder sb = new StringBuilder();
        sb.append(e.getClass().getSimpleName()).append(": ").append(e.getMessage()).append("\n");
        
        StackTraceElement[] stackTrace = e.getStackTrace();
        for (int i = 0; i < Math.min(stackTrace.length, 3); i++) { // 只取前3行
            sb.append("\tat ").append(stackTrace[i]).append("\n");
        }
        
        return sb.toString();
    }

    /**
     * 获取请求的IP地址
     */
    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
