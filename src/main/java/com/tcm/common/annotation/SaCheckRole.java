package com.tcm.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 角色权限校验注解
 * 用于方法级别的角色权限控制
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface SaCheckRole {
    
    /**
     * 需要校验的角色标识
     */
    String[] value() default {};
    
    /**
     * 验证模式：AND | OR，默认AND
     */
    String mode() default "AND";
}
