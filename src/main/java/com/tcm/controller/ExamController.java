package com.tcm.controller;

import com.tcm.common.Result;
import com.tcm.entity.ExamAnswer;
import com.tcm.service.ExamService;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 考试相关接口
 */
@RestController
@RequestMapping("/exam")
public class ExamController {

    @Autowired
    private ExamService examService;

    /**
     * 获取试卷题目
     */
    @GetMapping("/questions")
    public Result getQuestions(@RequestParam Long examId) {
        return Result.success(examService.getRandomQuestions(examId));
    }

    /**
     * 开始考试
     */
    @PostMapping("/start")
    public Result startExam(@RequestBody Map<String, Object> params) {
        Long userId = Long.valueOf(params.get("userId").toString());
        Long examId = Long.valueOf(params.get("examId").toString());
        return Result.success(examService.startExam(userId, examId));
    }

    /**
     * 保存答案
     */
    @PostMapping("/save-answer")
    public Result saveAnswer(@RequestBody ExamAnswer answer) {
        examService.saveAnswer(answer);
        return Result.success();
    }

    /**
     * 提交试卷
     */
    @PostMapping("/submit")
    public Result submitExam(@RequestBody Map<String, Object> params) {
        Long recordId = Long.valueOf(params.get("recordId").toString());
        return Result.success(examService.submitAndCorrect(recordId));
    }

    /**
     * 获取考试结果
     */
    @GetMapping("/result/{recordId}")
    public Result getExamResult(@PathVariable Long recordId) {
        return Result.success(examService.getExamResult(recordId));
    }

    /**
     * 获取考试历史记录
     */
    @GetMapping("/history/{userId}")
    public Result getExamHistory(@PathVariable Long userId) {
        return Result.success(examService.getExamHistory(userId));
    }

    /**
     * 获取考试列表
     */
    @GetMapping("/list")
    public Result getExamList(@RequestParam(defaultValue = "1") Integer page,
                              @RequestParam(defaultValue = "10") Integer size,
                              @RequestParam(required = false) String name,
                              @RequestParam(required = false) String type) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", page);
        params.put("size", size);
        params.put("name", name);
        params.put("type", type);
        return Result.success(examService.getExamList(params));
    }

    /**
     * 新增考试
     */
    @PostMapping("/add")
    public Result addExam(@RequestBody Map<String, Object> params) {
        examService.addExam(params);
        return Result.success();
    }

    /**
     * 更新考试
     */
    @PostMapping("/update")
    public Result updateExam(@RequestBody Map<String, Object> params) {
        examService.updateExam(params);
        return Result.success();
    }

    /**
     * 删除考试
     */
    @PostMapping("/delete/{id}")
    public Result deleteExam(@PathVariable Long id) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        examService.deleteExam(params);
        return Result.success();
    }

    /**
     * 获取所有考试题目
     */
    @GetMapping("/getAllExam")
    public Result getAllExam() {
        return Result.success(examService.getAllExam());
    }

    /**
     * 获取考试详情
     */
    @GetMapping("/detail/{examId}")
    public Result getExamDetail(@PathVariable Long examId) {
        return Result.success(examService.getExamDetail(examId));
    }

    /**
     * 发布考试
     */
    @PostMapping("/publish/{examId}")
    public Result publishExam(@PathVariable Long examId) {
        examService.publishExam(examId);
        return Result.success();
    }

    /**
     * 下架考试
     */
    @PostMapping("/unpublish/{examId}")
    public Result unpublishExam(@PathVariable Long examId) {
        examService.unpublishExam(examId);
        return Result.success();
    }

    /**
     * 获取分类下的考试列表
     */
    @GetMapping("/category/{category}")
    public Result getExamsByCategory(@PathVariable String category) {
        return Result.success(examService.getExamsByCategory(category));
    }
}
