package com.tcm.controller;

import com.tcm.common.Result;
import com.tcm.common.utils.RedisUtil;
import com.tcm.dto.ExamLeaderboardDTO;
import com.tcm.entity.User;
import com.tcm.service.AsyncStatsService;
import com.tcm.service.CourseService;
import com.tcm.service.ExamRecordService;
import com.tcm.service.InformationService;
import com.tcm.service.UserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/dashboard")
public class HomeController {

    @Resource
    private UserService userService;
    @Resource
    private CourseService courseService;
    @Resource
    private InformationService informationService;
    @Resource
    private ExamRecordService examRecordService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private AsyncStatsService asyncStatsService;

    // Redis中存储考试总排行榜的key
    private static final String EXAM_TOTAL_RANKING_KEY = "exam:ranking:total";

    /**
     * 获取统计数据 - 使用线程池并行查询优化性能
     */
    @GetMapping("/stats")
    public Result getStats() {
        try {
            log.debug("开始获取统计数据");
            long startTime = System.currentTimeMillis();

            // 并行执行所有统计查询
            CompletableFuture<AsyncStatsService.UserStats> userStatsFuture = asyncStatsService.getUserStats();
            CompletableFuture<AsyncStatsService.CourseStats> courseStatsFuture = asyncStatsService.getCourseStats();
            CompletableFuture<AsyncStatsService.ResourceStats> resourceStatsFuture = asyncStatsService.getResourceStats();
            CompletableFuture<AsyncStatsService.ExamStats> examStatsFuture = asyncStatsService.getExamStats();

            // 等待所有查询完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                userStatsFuture, courseStatsFuture, resourceStatsFuture, examStatsFuture
            );

            // 组装结果
            Map<String, Object> stats = new HashMap<>();

            allFutures.thenRun(() -> {
                try {
                    AsyncStatsService.UserStats userStats = userStatsFuture.get();
                    AsyncStatsService.CourseStats courseStats = courseStatsFuture.get();
                    AsyncStatsService.ResourceStats resourceStats = resourceStatsFuture.get();
                    AsyncStatsService.ExamStats examStats = examStatsFuture.get();

                    stats.put("userCount", userStats.getTotalUsers());
                    stats.put("newUsers", userStats.getNewUsers());
                    stats.put("courseCount", courseStats.getCourseCount());
                    stats.put("courseViews", courseStats.getCourseViews());
                    stats.put("resourceCount", resourceStats.getResourceCount());
                    stats.put("pendingReview", resourceStats.getPendingReview());
                    stats.put("todayExamUsers", examStats.getTodayExamUsers());
                    stats.put("avgScore", examStats.getAvgScore());
                } catch (Exception e) {
                    log.error("组装统计数据失败", e);
                }
            }).get(); // 同步等待完成

            long endTime = System.currentTimeMillis();
            log.info("统计数据获取完成，总耗时: {}ms", endTime - startTime);

            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            // 返回默认值
            Map<String, Object> defaultStats = new HashMap<>();
            defaultStats.put("userCount", 0L);
            defaultStats.put("newUsers", 0L);
            defaultStats.put("courseCount", 0L);
            defaultStats.put("courseViews", 0L);
            defaultStats.put("resourceCount", 0L);
            defaultStats.put("pendingReview", 0L);
            defaultStats.put("todayExamUsers", 0L);
            defaultStats.put("avgScore", 0.0);
            return Result.success(defaultStats);
        }
    }

    /**
     * 获取最新课程
     */
    @GetMapping("/latestCourses")
    public Result getLatestCourses() {
        List<Map<String, Object>> courses = courseService.findLatest(4).stream()
                .map(course -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("name", course.getName());
                    map.put("createTime", course.getCreateTime().format(
                            DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    return map;
                })
                .collect(Collectors.toList());
        return Result.success(courses);
    }

    /**
     * 获取用户活跃度数据
     */
    @GetMapping("/userActivity")
    public Result getUserActivity() {
        List<Map<String, Object>> activityData = new ArrayList<>();
        LocalDate today = LocalDate.now();

        // 获取近7天的数据
        for (int i = 6; i >= 0; i--) {
            LocalDate date = today.minusDays(i);
            Map<String, Object> data = new HashMap<>();
            data.put("date", date.format(DateTimeFormatter.ofPattern("MM-dd")));
            data.put("count", userService.countActiveUsers(date));
            activityData.add(data);
        }

        return Result.success(activityData);
    }

    /**
     * 获取课程分类数据
     */
    @GetMapping("/courseTypes")
    public Result getCourseTypes() {
        List<Map<String, Object>> typeData = courseService.getCourseTypeStats().stream()
                .map(stat -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("name", stat.getTypeName());
                    map.put("value", stat.getCount());
                    return map;
                })
                .collect(Collectors.toList());
        return Result.success(typeData);
    }

    /**
     * 获取考试得分排行榜
     */
    @GetMapping("/examLeaderboard")
    public Result getExamLeaderboard() {
        // 首先尝试从Redis中获取排行榜数据（按分数从高到低排序）
        Set<ZSetOperations.TypedTuple<String>> topScoreSet = redisUtil.reverseRangeWithScores(EXAM_TOTAL_RANKING_KEY, 0, 9);
        
        List<ExamLeaderboardDTO> leaderboard = new ArrayList<>();
        
        // 如果Redis中存在数据，则直接使用
        if (topScoreSet != null && !topScoreSet.isEmpty()) {
            for (ZSetOperations.TypedTuple<String> tuple : topScoreSet) {
                String userId = tuple.getValue();
                Double score = tuple.getScore();
                
                if (userId != null && score != null) {
                    User user = userService.getUserById(Long.valueOf(userId));
                    if (user != null) {
                        ExamLeaderboardDTO dto = new ExamLeaderboardDTO();
                        dto.setUsername(user.getUsername());
                        dto.setName(user.getName() != null ? user.getName() : user.getUsername());
                        dto.setAvatar(user.getAvatar());
                        dto.setScore(score.intValue());
                        leaderboard.add(dto);
                    }
                }
            }
        } else {
            // 如果Redis中没有数据，则从数据库中获取，并存入Redis
            leaderboard = examRecordService.findTopScoreUsers(10);
            
            // 将数据同步到Redis中
            for (ExamLeaderboardDTO dto : leaderboard) {
                User user = userService.getUserByUsername(dto.getUsername());
                if (user != null) {
                    redisUtil.zAdd(EXAM_TOTAL_RANKING_KEY, user.getId().toString(), dto.getScore().longValue());
                }
            }
            
            // 设置总排行榜缓存30天过期
            redisUtil.expire(EXAM_TOTAL_RANKING_KEY, 30, TimeUnit.DAYS);
        }
        
        return Result.success(leaderboard);
    }
}
