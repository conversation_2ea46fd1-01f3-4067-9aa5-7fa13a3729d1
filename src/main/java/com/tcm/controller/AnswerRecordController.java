package com.tcm.controller;

import com.github.pagehelper.PageInfo;
import com.tcm.common.Result;
import com.tcm.dto.TestErrorCount;
import com.tcm.entity.AnswerRecord;
import com.tcm.service.AnswerRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 答题信息接口
 **/

@RestController
@RequestMapping("/answerRecord")
public class AnswerRecordController {

    @Autowired
    private AnswerRecordService answerRecordService;

    /**
     * 新增
     */
    @PostMapping("/add")
    public Result add(@RequestBody AnswerRecord answerRecord) {
        answerRecordService.insert(answerRecord);
        return Result.success();
    }

    /**
     * 删除
     */
    @DeleteMapping("/delete/{id}")
    public Result deleteById(@PathVariable Integer id) {
        answerRecordService.deleteById(id);
        return Result.success();
    }

    /**
     * 批量删除
     */
    @DeleteMapping("/delete/batch")
    public Result deleteBatch(@RequestBody List<Integer> ids) {
        answerRecordService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 修改
     */
    @PutMapping("/update")
    public Result updateById(@RequestBody AnswerRecord answerRecord) {
        answerRecordService.updateById(answerRecord);
        return Result.success();
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/selectById/{id}")
    public Result selectById(@PathVariable Integer id) {
        AnswerRecord answerRecord = answerRecordService.selectById(id);
        return Result.success(answerRecord);
    }

    /**
     * 查询所有
     */
    @GetMapping("/selectAll")
    public Result selectAll(AnswerRecord answerRecord) {
        List<AnswerRecord> list = answerRecordService.selectAll(answerRecord);
        return Result.success(list);
    }

    /**
     * 分页查询
     */
    @GetMapping("/selectPage")
    public Result selectPage(AnswerRecord answerRecord,
                             @RequestParam(defaultValue = "1") Integer pageNum,
                             @RequestParam(defaultValue = "10") Integer pageSize) {
        PageInfo<AnswerRecord> page = answerRecordService.selectPage(answerRecord, pageNum, pageSize);
        return Result.success(page);
    }

    /**
     * 高频错题top8
     */
    @GetMapping("/selectTop8")
    public Result selectTop8() {
        List<TestErrorCount> list = answerRecordService.selectTop8IncorrectAnswers();
        return Result.success(list);
    }

}