package com.tcm.controller;

import com.tcm.common.Result;
import com.tcm.entity.CourseTestRecord;
import com.tcm.service.CourseTestService;
import com.tcm.service.QuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 课程测试控制器
 * 处理课程详情页的测试功能
 */
@RestController
@RequestMapping("/course/test")
public class CourseTestController {

    @Autowired
    private QuestionService questionService;
    
    @Autowired
    private CourseTestService courseTestService;

    /**
     * 获取随机测试题目
     * @param type 题目类型，single(单选题)
     * @param count 题目数量，默认为5
     * @return 随机题目列表
     */
    @GetMapping("/questions")
    public Result getRandomQuestions(
            @RequestParam(value = "type", defaultValue = "single") String type,
            @RequestParam(value = "count", defaultValue = "5") Integer count) {
        return Result.success(questionService.getRandomQuestionsByType(type, count));
    }
    
    /**
     * 提交测试结果
     * @param params 包含用户ID、课程ID、分数、答案等信息的参数
     * @return 操作结果
     */
    @PostMapping("/submit")
    public Result submitTestResult(@RequestBody Map<String, Object> params) {
        Map<String, Object> result = courseTestService.calculateTestScore(params);
        return Result.success(result);
    }
    
    /**
     * 获取测试记录
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 测试记录
     */
    @GetMapping("/records")
    public Result getTestRecords(
            @RequestParam Integer userId,
            @RequestParam(required = false) Integer courseId) {
        
        List<CourseTestRecord> records = courseTestService.getUserTestRecords(userId, courseId);
        return Result.success(records);
    }
    
    /**
     * 保存测试记录
     * @param record 测试记录
     * @return 操作结果
     */
    @PostMapping("/save")
    public Result saveTestRecord(@RequestBody CourseTestRecord record) {
        boolean result = courseTestService.saveTestRecord(record);
        return result ? Result.success() : Result.error("500", "保存测试记录失败");
    }
} 