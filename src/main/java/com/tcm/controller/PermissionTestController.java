package com.tcm.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.tcm.common.Result;
import com.tcm.service.SaTokenAuthService;
import com.tcm.service.PermissionService;
import com.tcm.common.config.PermissionStrategy;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 权限测试控制器 - 用于验证权限修复
 */
@Slf4j
@Tag(name = "权限测试接口", description = "用于验证权限修复的测试接口")
@RestController
@RequestMapping("/permission-test")
public class PermissionTestController {

    @Autowired
    private SaTokenAuthService saTokenAuthService;
    
    @Autowired
    private PermissionService permissionService;
    
    @Autowired
    private PermissionStrategy permissionStrategy;

    @Operation(summary = "测试权限检查", description = "测试当前用户的权限检查功能")
    @GetMapping("/permission")
    public Result testPermission(@RequestParam(defaultValue = "user:delete") String permission) {
        try {
            if (!StpUtil.isLogin()) {
                return Result.error("401", "用户未登录");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("loginId", StpUtil.getLoginId());
            result.put("permission", permission);
            
            // 测试Sa-Token权限检查
            boolean hasPermissionSaToken = saTokenAuthService.hasPermission(permission);
            result.put("saTokenResult", hasPermissionSaToken);
            
            // 测试权限策略
            boolean hasAccessStrategy = permissionStrategy.hasAccess(permission);
            result.put("strategyResult", hasAccessStrategy);
            
            // 测试是否为管理员
            boolean isAdmin = permissionStrategy.isAdmin();
            result.put("isAdmin", isAdmin);
            
            // 测试是否为超级管理员
            boolean isSuperAdmin = permissionStrategy.isSuperAdmin();
            result.put("isSuperAdmin", isSuperAdmin);

            log.info("权限测试结果: {}", result);
            return Result.success(result);
        } catch (Exception e) {
            log.error("权限测试失败", e);
            return Result.error("500", "权限测试失败: " + e.getMessage());
        }
    }

    @Operation(summary = "测试角色检查", description = "测试当前用户的角色检查功能")
    @GetMapping("/role")
    public Result testRole(@RequestParam(defaultValue = "ADMIN") String role) {
        try {
            if (!StpUtil.isLogin()) {
                return Result.error("401", "用户未登录");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("loginId", StpUtil.getLoginId());
            result.put("role", role);
            
            // 测试Sa-Token角色检查
            boolean hasRoleSaToken = saTokenAuthService.hasRole(role);
            result.put("saTokenResult", hasRoleSaToken);
            
            // 获取用户信息
            try {
                Object userInfo = StpUtil.getSession().get("userInfo");
                result.put("userInfo", userInfo);
            } catch (Exception e) {
                result.put("userInfo", "获取失败: " + e.getMessage());
            }

            log.info("角色测试结果: {}", result);
            return Result.success(result);
        } catch (Exception e) {
            log.error("角色测试失败", e);
            return Result.error("500", "角色测试失败: " + e.getMessage());
        }
    }

    @Operation(summary = "测试AI消息处理", description = "测试AI消息的JSON构建")
    @PostMapping("/ai-message")
    public Result testAiMessage(@RequestBody Map<String, String> params) {
        try {
            String message = params.get("message");
            
            Map<String, Object> result = new HashMap<>();
            result.put("originalMessage", message);
            
            if (message != null) {
                // 模拟AI控制器中的消息处理逻辑
                String cleanMessage = message.trim()
                    .replaceAll("[\\r\\n\\t]", " ")  // 替换换行符和制表符
                    .replaceAll("\\s+", " ")        // 合并多个空格
                    .replaceAll("[\"\\\\]", "");     // 移除可能导致JSON解析问题的字符
                
                if (cleanMessage.length() > 1000) {
                    cleanMessage = cleanMessage.substring(0, 1000) + "...";
                }
                
                String prompt = "你是一名专业的中医，请用中医理论和知识精炼的回答以下问题：" + cleanMessage;
                
                result.put("cleanMessage", cleanMessage);
                result.put("prompt", prompt);
                result.put("promptLength", prompt.length());
                
                // 测试JSON序列化
                try {
                    com.alibaba.fastjson.JSON.toJSONString(result);
                    result.put("jsonSerializable", true);
                } catch (Exception e) {
                    result.put("jsonSerializable", false);
                    result.put("jsonError", e.getMessage());
                }
            }
            
            log.info("AI消息测试结果: {}", result);
            return Result.success(result);
        } catch (Exception e) {
            log.error("AI消息测试失败", e);
            return Result.error("500", "AI消息测试失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @GetMapping("/user-info")
    public Result getCurrentUserInfo() {
        try {
            if (!StpUtil.isLogin()) {
                return Result.error("401", "用户未登录");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("loginId", StpUtil.getLoginId());
            result.put("tokenValue", StpUtil.getTokenValue());
            
            // 获取Session中的用户信息
            try {
                Object userInfo = StpUtil.getSession().get("userInfo");
                result.put("sessionUserInfo", userInfo);
            } catch (Exception e) {
                result.put("sessionUserInfo", "获取失败: " + e.getMessage());
            }
            
            // 获取角色列表
            try {
                result.put("roleList", StpUtil.getRoleList());
            } catch (Exception e) {
                result.put("roleList", "获取失败: " + e.getMessage());
            }
            
            // 获取权限列表
            try {
                result.put("permissionList", StpUtil.getPermissionList());
            } catch (Exception e) {
                result.put("permissionList", "获取失败: " + e.getMessage());
            }

            log.info("用户信息: {}", result);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return Result.error("500", "获取用户信息失败: " + e.getMessage());
        }
    }
}
