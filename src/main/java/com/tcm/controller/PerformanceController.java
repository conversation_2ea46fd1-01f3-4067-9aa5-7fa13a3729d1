package com.tcm.controller;

import com.tcm.common.Result;
import com.tcm.service.CacheService;
import com.tcm.service.PerformanceMetricsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.ThreadMXBean;
import java.util.HashMap;
import java.util.Map;

/**
 * 性能监控控制器
 * 提供性能指标查询和缓存管理接口
 */
@Slf4j
@RestController
@RequestMapping("/performance")
public class PerformanceController {

    @Autowired
    private PerformanceMetricsService performanceMetricsService;

    @Autowired
    private CacheService cacheService;

    /**
     * 获取性能指标报告
     */
    @GetMapping("/metrics")
    public Result getPerformanceMetrics() {
        try {
            String report = performanceMetricsService.getPerformanceReport();
            return Result.success(report);
        } catch (Exception e) {
            log.error("获取性能指标失败", e);
            return Result.error("500", "获取性能指标失败");
        }
    }

    /**
     * 获取系统资源使用情况
     */
    @GetMapping("/system")
    public Result getSystemInfo() {
        try {
            Map<String, Object> systemInfo = new HashMap<>();
            
            // 内存信息
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
            long freeMemory = maxMemory - usedMemory;
            
            Map<String, Object> memoryInfo = new HashMap<>();
            memoryInfo.put("used", usedMemory / 1024 / 1024); // MB
            memoryInfo.put("max", maxMemory / 1024 / 1024);   // MB
            memoryInfo.put("free", freeMemory / 1024 / 1024); // MB
            memoryInfo.put("usagePercent", maxMemory > 0 ? (double) usedMemory / maxMemory * 100 : 0);
            
            systemInfo.put("memory", memoryInfo);
            
            // 线程信息
            ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
            Map<String, Object> threadInfo = new HashMap<>();
            threadInfo.put("count", threadBean.getThreadCount());
            threadInfo.put("peak", threadBean.getPeakThreadCount());
            threadInfo.put("daemon", threadBean.getDaemonThreadCount());
            
            systemInfo.put("threads", threadInfo);
            
            // JVM信息
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> jvmInfo = new HashMap<>();
            jvmInfo.put("processors", runtime.availableProcessors());
            jvmInfo.put("javaVersion", System.getProperty("java.version"));
            jvmInfo.put("jvmName", System.getProperty("java.vm.name"));
            
            systemInfo.put("jvm", jvmInfo);
            
            return Result.success(systemInfo);
        } catch (Exception e) {
            log.error("获取系统信息失败", e);
            return Result.error("500", "获取系统信息失败");
        }
    }

    /**
     * 获取缓存统计信息
     */
    @GetMapping("/cache/stats")
    public Result getCacheStats() {
        try {
            String stats = cacheService.getCacheStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取缓存统计失败", e);
            return Result.error("500", "获取缓存统计失败");
        }
    }

    /**
     * 清空指定类型的缓存
     */
    @DeleteMapping("/cache/{cacheType}")
    public Result evictCache(@PathVariable String cacheType) {
        try {
            cacheService.evictAll(cacheType);
            log.info("已清空{}类型的缓存", cacheType);
            return Result.success("缓存清空成功");
        } catch (Exception e) {
            log.error("清空缓存失败", e);
            return Result.error("500", "清空缓存失败");
        }
    }

    /**
     * 清空指定键的缓存
     */
    @DeleteMapping("/cache/{cacheType}/{key}")
    public Result evictCacheKey(@PathVariable String cacheType, @PathVariable String key) {
        try {
            cacheService.evict(cacheType, key);
            log.info("已清空缓存: {}:{}", cacheType, key);
            return Result.success("缓存清空成功");
        } catch (Exception e) {
            log.error("清空缓存失败", e);
            return Result.error("500", "清空缓存失败");
        }
    }

    /**
     * 预热缓存
     */
    @PostMapping("/cache/warmup")
    public Result warmupCache(@RequestBody Map<String, Object> params) {
        try {
            String cacheType = (String) params.get("cacheType");
            String key = (String) params.get("key");
            Object data = params.get("data");

            if (cacheType == null || key == null || data == null) {
                return Result.error("400", "参数不完整");
            }

            cacheService.warmUp(cacheType, key, data, 30, java.util.concurrent.TimeUnit.MINUTES);
            log.info("缓存预热完成: {}:{}", cacheType, key);
            return Result.success("缓存预热成功");
        } catch (Exception e) {
            log.error("缓存预热失败", e);
            return Result.error("500", "缓存预热失败");
        }
    }

    /**
     * 重置性能计数器
     */
    @PostMapping("/metrics/reset")
    public Result resetMetrics() {
        try {
            performanceMetricsService.resetCounters();
            return Result.success("性能计数器重置成功");
        } catch (Exception e) {
            log.error("重置性能计数器失败", e);
            return Result.error("500", "重置性能计数器失败");
        }
    }

    /**
     * 触发垃圾回收
     */
    @PostMapping("/system/gc")
    public Result triggerGC() {
        try {
            long beforeMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
            
            System.gc();
            Thread.sleep(1000); // 等待GC完成
            
            long afterMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
            long freedMemory = beforeMemory - afterMemory;
            
            Map<String, Object> result = new HashMap<>();
            result.put("beforeMemory", beforeMemory / 1024 / 1024); // MB
            result.put("afterMemory", afterMemory / 1024 / 1024);   // MB
            result.put("freedMemory", freedMemory / 1024 / 1024);   // MB
            
            log.info("手动触发GC完成，释放内存: {}MB", freedMemory / 1024 / 1024);
            return Result.success(result);
        } catch (Exception e) {
            log.error("触发GC失败", e);
            return Result.error("500", "触发GC失败");
        }
    }

    /**
     * 获取性能建议
     */
    @GetMapping("/suggestions")
    public Result getPerformanceSuggestions() {
        try {
            Map<String, Object> suggestions = new HashMap<>();

            // 内存使用建议
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
            double memoryUsagePercent = maxMemory > 0 ? (double) usedMemory / maxMemory * 100 : 0;

            if (memoryUsagePercent > 80) {
                suggestions.put("memory", "内存使用率过高，建议增加JVM堆内存或优化内存使用");
            } else if (memoryUsagePercent < 30) {
                suggestions.put("memory", "内存使用率较低，可以考虑减少JVM堆内存分配");
            } else {
                suggestions.put("memory", "内存使用率正常");
            }

            // 线程使用建议
            ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
            int threadCount = threadBean.getThreadCount();

            if (threadCount > 200) {
                suggestions.put("threads", "线程数量过多，建议检查线程池配置和线程泄漏");
            } else {
                suggestions.put("threads", "线程数量正常");
            }

            // 缓存建议
            suggestions.put("cache", "定期监控缓存命中率，适当调整缓存策略");

            // 数据库建议
            suggestions.put("database", "监控慢查询，添加必要的索引，使用批量操作");

            return Result.success(suggestions);
        } catch (Exception e) {
            log.error("获取性能建议失败", e);
            return Result.error("500", "获取性能建议失败");
        }
    }
}
