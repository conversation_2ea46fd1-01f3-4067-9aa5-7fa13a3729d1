package com.tcm.controller;

import cn.hutool.core.util.StrUtil;
import com.tcm.common.config.XfConfig;
import com.tcm.component.MemoryUserRecordSpace;
import com.tcm.component.XfStreamClient;
import com.tcm.dto.InteractMsg;
import com.tcm.dto.MsgDTO;
import com.tcm.listener.XfWebSocketListener;
import java.util.List;
import java.util.UUID;
import okhttp3.WebSocket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/xunfei")
public class XfController {

    @Autowired
    private XfConfig xfConfig;

    @Autowired
    private XfStreamClient xfStreamClient;

    @Autowired
    private MemoryUserRecordSpace memoryUserRecordSpace;

    @GetMapping("/sendQuestion")
    public String question(@RequestParam("id") Long id, @RequestParam("question") String question) throws InterruptedException {
        if (StrUtil.isBlank(question)) {
            return "无效问题，请重新输入";
        }
        if (!memoryUserRecordSpace.tryLock(id)) {
            return "正在处理上次问题，请稍后再试";
        }
        if (!xfStreamClient.operateToken(XfStreamClient.GET_TOKEN_STATUS)) {
            memoryUserRecordSpace.unLock(id);
            return "当前大模型连接数过多，请稍后再试";
        }

        MsgDTO msgDTO = MsgDTO.createUserMsg(question);
        XfWebSocketListener listener = new XfWebSocketListener();
        List<MsgDTO> msgList = memoryUserRecordSpace.getAllInteractMsg(id);
        msgList.add(msgDTO);
        WebSocket webSocket = xfStreamClient.sendMsg(UUID.randomUUID().toString().substring(0, 10), msgList, listener);
        if (webSocket == null) {
            // 归还令牌
            xfStreamClient.operateToken(XfStreamClient.BACK_TOKEN_STATUS);
            // 释放锁
            memoryUserRecordSpace.unLock(id);
            return "系统内部错误，请联系管理员";
        }
        try {
            int count = 0;
            // 为了避免死循环，设置循环次数来定义超时时长
            int maxCount = xfConfig.getMaxResponseTime() * 5;
            while (count <= maxCount) {
                Thread.sleep(200);
                if (listener.isWsCloseFlag()) {
                    break;
                }
                count++;
            }
            if (count > maxCount) {
                return "大模型响应超时，请联系管理员";
            }
            String answer = listener.getAnswer().toString();
            memoryUserRecordSpace.storeInteractMsg(id, new InteractMsg(MsgDTO.createUserMsg(question), MsgDTO.createAssistantMsg(answer)));
            return answer;
        } finally {
            // 关闭连接
            webSocket.close(1000, "");
            // 释放锁
            memoryUserRecordSpace.unLock(id);
            // 归还令牌
            xfStreamClient.operateToken(XfStreamClient.BACK_TOKEN_STATUS);
        }
    }

}
