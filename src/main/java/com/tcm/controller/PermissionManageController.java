package com.tcm.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import com.tcm.common.Result;
import com.tcm.dto.PermissionTreeDTO;
import com.tcm.dto.RolePermissionAssignDTO;
import com.tcm.dto.UserRoleAssignDTO;
import com.tcm.entity.Permission;
import com.tcm.entity.Role;
import com.tcm.service.PermissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 权限管理控制器
 * 提供可视化的权限分配和管理功能
 */
@Slf4j
@RestController
@RequestMapping("/permission/manage")
@Api(tags = "权限分配管理接口")
public class PermissionManageController {

    @Autowired
    private PermissionService permissionService;

    /**
     * 获取权限树结构
     */
    @GetMapping("/tree")
    @ApiOperation("获取权限树结构")
    @SaCheckRole("SUPER_ADMIN")
    public Result getPermissionTree() {
        try {
            List<PermissionTreeDTO> tree = permissionService.getPermissionTree();
            return Result.success(tree);
        } catch (Exception e) {
            log.error("获取权限树失败", e);
            return Result.error("500", "获取权限树失败");
        }
    }

    /**
     * 获取角色的权限树结构
     */
    @GetMapping("/role/{roleId}/tree")
    @ApiOperation("获取角色的权限树结构")
    @SaCheckRole("SUPER_ADMIN")
    public Result getRolePermissionTree(@PathVariable Integer roleId) {
        try {
            List<PermissionTreeDTO> tree = permissionService.getRolePermissionTree(roleId);
            return Result.success(tree);
        } catch (Exception e) {
            log.error("获取角色权限树失败，roleId: {}", roleId, e);
            return Result.error("500", "获取角色权限树失败");
        }
    }

    /**
     * 获取用户详细信息（包含角色和权限）
     */
    @GetMapping("/user/{userId}/{userType}/detail")
    @ApiOperation("获取用户详细信息")
    @SaCheckRole("SUPER_ADMIN")
    public Result getUserDetailInfo(@PathVariable Integer userId, @PathVariable String userType) {
        try {
            Map<String, Object> userInfo = permissionService.getUserDetailInfo(userId, userType);
            return Result.success(userInfo);
        } catch (Exception e) {
            log.error("获取用户详细信息失败，userId: {}, userType: {}", userId, userType, e);
            return Result.error("500", "获取用户详细信息失败");
        }
    }

    /**
     * 为用户分配角色
     */
    @PostMapping("/user/assign-roles")
    @ApiOperation("为用户分配角色")
    @SaCheckRole("SUPER_ADMIN")
    public Result assignRolesToUser(@RequestBody UserRoleAssignDTO dto) {
        try {
            boolean success;
            if ("assign".equals(dto.getOperation())) {
                success = permissionService.assignRolesToUser(dto.getUserId(), dto.getUserType(), dto.getRoleIds());
            } else if ("revoke".equals(dto.getOperation())) {
                success = permissionService.revokeRolesFromUser(dto.getUserId(), dto.getUserType(), dto.getRoleIds());
            } else {
                return Result.error("400", "不支持的操作类型");
            }

            if (success) {
                log.info("用户角色操作成功，userId: {}, userType: {}, operation: {}", 
                        dto.getUserId(), dto.getUserType(), dto.getOperation());
                return Result.success("操作成功");
            } else {
                return Result.error("500", "操作失败");
            }
        } catch (Exception e) {
            log.error("用户角色操作失败", e);
            return Result.error("500", "操作失败");
        }
    }

    /**
     * 为角色分配权限
     */
    @PostMapping("/role/assign-permissions")
    @ApiOperation("为角色分配权限")
    @SaCheckRole("SUPER_ADMIN")
    public Result assignPermissionsToRole(@RequestBody RolePermissionAssignDTO dto) {
        try {
            boolean success;
            if ("assign".equals(dto.getOperation())) {
                success = permissionService.assignPermissionsToRole(dto.getRoleId(), dto.getPermissionIds());
            } else if ("revoke".equals(dto.getOperation())) {
                success = permissionService.revokePermissionsFromRole(dto.getRoleId(), dto.getPermissionIds());
            } else if ("replace".equals(dto.getOperation())) {
                success = permissionService.replaceRolePermissions(dto.getRoleId(), dto.getPermissionIds());
            } else {
                return Result.error("400", "不支持的操作类型");
            }

            if (success) {
                log.info("角色权限操作成功，roleId: {}, operation: {}", dto.getRoleId(), dto.getOperation());
                return Result.success("操作成功");
            } else {
                return Result.error("500", "操作失败");
            }
        } catch (Exception e) {
            log.error("角色权限操作失败", e);
            return Result.error("500", "操作失败");
        }
    }

    /**
     * 获取所有角色列表（用于分配）
     */
    @GetMapping("/roles/all")
    @ApiOperation("获取所有角色列表")
    @SaCheckRole("SUPER_ADMIN")
    public Result getAllRoles() {
        try {
            List<Role> roles = permissionService.getAllRoles();
            return Result.success(roles);
        } catch (Exception e) {
            log.error("获取角色列表失败", e);
            return Result.error("500", "获取角色列表失败");
        }
    }

    /**
     * 获取所有权限列表（用于分配）
     */
    @GetMapping("/permissions/all")
    @ApiOperation("获取所有权限列表")
    @SaCheckPermission("permission:view")
    public Result getAllPermissions() {
        try {
            List<Permission> permissions = permissionService.getAllPermissions();
            return Result.success(permissions);
        } catch (Exception e) {
            log.error("获取权限列表失败", e);
            return Result.error("500", "获取权限列表失败");
        }
    }

    /**
     * 获取用户当前角色
     */
    @GetMapping("/user/{userId}/{userType}/roles")
    @ApiOperation("获取用户当前角色")
    @SaCheckPermission("permission:view")
    public Result getUserRoles(@PathVariable Integer userId, @PathVariable String userType) {
        try {
            List<Role> roles = permissionService.getUserRoles(userId, userType);
            return Result.success(roles);
        } catch (Exception e) {
            log.error("获取用户角色失败，userId: {}, userType: {}", userId, userType, e);
            return Result.error("500", "获取用户角色失败");
        }
    }

    /**
     * 获取角色当前权限
     */
    @GetMapping("/role/{roleId}/permissions")
    @ApiOperation("获取角色当前权限")
    @SaCheckPermission("permission:view")
    public Result getRolePermissions(@PathVariable Integer roleId) {
        try {
            List<Permission> permissions = permissionService.getPermissionsByRole(String.valueOf(roleId));
            return Result.success(permissions);
        } catch (Exception e) {
            log.error("获取角色权限失败，roleId: {}", roleId, e);
            return Result.error("500", "获取角色权限失败");
        }
    }
}
