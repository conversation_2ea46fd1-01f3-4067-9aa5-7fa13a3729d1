package com.tcm.controller;

import com.tcm.common.Result;
import com.tcm.common.enums.ResultCodeEnum;
import com.tcm.service.QuestionService;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/exam/question")
public class QuestionController {

    private static final Logger log = LoggerFactory.getLogger(QuestionController.class);

    @Autowired
    private QuestionService questionService;

    /**
     * 获取题目列表
     */
    @GetMapping("/list")
    public Result getQuestionList(
            @RequestParam Long examId,
            @RequestParam String type,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        return Result.success(questionService.getQuestionList(examId, type, page, size));
    }

    /**
     * 新增题目
     */
    @PostMapping("/add")
    public Result addQuestion(@RequestBody Map<String, Object> params) {
        questionService.addQuestion(params);
        return Result.success();
    }

    /**
     * 更新题目
     */
    @PostMapping("/update")
    public Result updateQuestion(@RequestBody Map<String, Object> params) {
        questionService.updateQuestion(params);
        return Result.success();
    }

    /**
     * 删除题目
     */
    @PostMapping("/delete/{id}")
    public Result deleteQuestion(@PathVariable Long id) {
        questionService.deleteQuestion(id);
        return Result.success();
    }

    /**
     * 批量导入题目
     */
    @PostMapping("/import")
    public Result importQuestions(
            @RequestParam("file") MultipartFile file,
            @RequestParam("examId") Long examId,
            @RequestParam("type") String type) {
        try {
            // 记录详细日志
            log.info("接收到文件上传请求: 文件名={}, 文件大小={}, examId={}, type={}", 
                    file != null ? file.getOriginalFilename() : "null", 
                    file != null ? file.getSize() : 0,
                    examId,
                    type);
            
            // 检查文件是否为空
            if (file == null || file.isEmpty()) {
                log.error("上传文件为空");
                return Result.error(ResultCodeEnum.FILE_EMPTY_ERROR);
            }

            // 检查文件格式
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !originalFilename.endsWith(".xlsx")) {
                log.error("文件格式不正确: {}", originalFilename);
                return Result.error(ResultCodeEnum.FILE_FORMAT_ERROR);
            }

            // 导入题目
            questionService.importQuestions(file, examId, type);
            log.info("文件导入成功: {}", originalFilename);
            return Result.success();
        } catch (Exception e) {
            log.error("导入失败", e);
            return Result.error(ResultCodeEnum.IMPORT_ERROR.code, "导入失败: " + e.getMessage());
        }
    }

    /**
     * 获取随机题目
     * @param type 题目类型
     * @param count 题目数量
     * @return 随机题目列表
     */
    @GetMapping("/random")
    public Result getRandomQuestions(
            @RequestParam("type") String type,
            @RequestParam(value = "count", defaultValue = "5") Integer count) {
        return Result.success(questionService.getRandomQuestionsByType(type, count));
    }
}