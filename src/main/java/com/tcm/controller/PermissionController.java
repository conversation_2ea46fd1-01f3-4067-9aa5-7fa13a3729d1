package com.tcm.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import com.tcm.common.Result;
import com.tcm.entity.Permission;
import com.tcm.entity.Role;
import com.tcm.service.PermissionService;
import com.tcm.service.SaTokenAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 权限管理控制器
 * 提供权限和角色的查询、管理功能
 */
@Slf4j
@RestController
@RequestMapping("/permission")
@Api(tags = "权限管理接口")
public class PermissionController {

    @Autowired
    private SaTokenAuthService saTokenAuthService;

    @Autowired
    private PermissionService permissionService;

    /**
     * 获取所有权限列表
     */
    @GetMapping("/list")
    @ApiOperation("获取所有权限列表")
    @SaCheckRole("ADMIN")
    public Result getPermissionList() {
        try {
            List<Permission> permissions = permissionService.getAllPermissions();
            return Result.success(permissions);
        } catch (Exception e) {
            log.error("获取权限列表失败", e);
            return Result.error("500", "获取权限列表失败");
        }
    }

    /**
     * 获取所有角色列表
     */
    @GetMapping("/roles")
    @ApiOperation("获取所有角色列表")
    @SaCheckRole("ADMIN")
    public Result getRoleList() {
        try {
            List<Role> roles = permissionService.getAllRoles();
            return Result.success(roles);
        } catch (Exception e) {
            log.error("获取角色列表失败", e);
            return Result.error("500", "获取角色列表失败");
        }
    }

    /**
     * 获取当前用户的权限信息
     */
    @GetMapping("/current")
    @ApiOperation("获取当前用户权限信息")
    public Result getCurrentUserPermissions() {
        try {
            if (!StpUtil.isLogin()) {
                return Result.error("401", "用户未登录");
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("permissions", StpUtil.getPermissionList());
            result.put("roles", StpUtil.getRoleList());
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取当前用户权限失败", e);
            return Result.error("500", "获取当前用户权限失败");
        }
    }

    /**
     * 检查当前用户是否有指定权限
     */
    @GetMapping("/check")
    @ApiOperation("检查用户权限")
    public Result checkPermission(@RequestParam String permission) {
        try {
            boolean hasPermission = saTokenAuthService.hasPermission(permission);
            Map<String, Object> result = new HashMap<>();
            result.put("hasPermission", hasPermission);
            result.put("permission", permission);
            return Result.success(result);
        } catch (Exception e) {
            log.error("权限检查失败", e);
            return Result.error("500", "权限检查失败");
        }
    }

    /**
     * 检查当前用户是否有指定角色
     */
    @GetMapping("/check/role")
    @ApiOperation("检查用户角色")
    public Result checkRole(@RequestParam String role) {
        try {
            boolean hasRole = saTokenAuthService.hasRole(role);
            Map<String, Object> result = new HashMap<>();
            result.put("hasRole", hasRole);
            result.put("role", role);
            return Result.success(result);
        } catch (Exception e) {
            log.error("角色检查失败", e);
            return Result.error("500", "角色检查失败");
        }
    }

    /**
     * 获取权限树结构（按分类组织）
     */
    @GetMapping("/tree")
    @ApiOperation("获取权限树结构")
    @SaCheckRole("ADMIN")
    public Result getPermissionTree() {
        try {
            Map<String, Map<String, String>> tree = new HashMap<>();
            
            // 用户管理权限
            Map<String, String> userPermissions = new HashMap<>();
            userPermissions.put("user:view", "查看用户");
            userPermissions.put("user:add", "添加用户");
            userPermissions.put("user:edit", "编辑用户");
            userPermissions.put("user:delete", "删除用户");
            tree.put("用户管理", userPermissions);
            
            // 课程管理权限
            Map<String, String> coursePermissions = new HashMap<>();
            coursePermissions.put("course:view", "查看课程");
            coursePermissions.put("course:selectAll", "查询所有课程");
            tree.put("课程管理", coursePermissions);
            
            // 考试管理权限
            Map<String, String> examPermissions = new HashMap<>();
            examPermissions.put("exam:view", "查看考试");
            examPermissions.put("exam:selectAll", "查询所有考试");
            examPermissions.put("exam:ai", "AI功能");
            examPermissions.put("exam:stream-chat", "AI聊天");
            tree.put("考试管理", examPermissions);
            
            // 系统管理权限
            Map<String, String> systemPermissions = new HashMap<>();
            systemPermissions.put("system:config", "系统配置");
            systemPermissions.put("dashboard:stats", "仪表板统计");
            tree.put("系统管理", systemPermissions);
            
            return Result.success(tree);
        } catch (Exception e) {
            log.error("获取权限树失败", e);
            return Result.error("500", "获取权限树失败");
        }
    }
}
