package com.tcm.dto;

import lombok.Data;

import java.util.List;

/**
 * 用户角色分配DTO
 */
@Data
public class UserRoleAssignDTO {
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 用户类型：admin, student
     */
    private String userType;
    
    /**
     * 角色ID列表
     */
    private List<Integer> roleIds;
    
    /**
     * 操作类型：assign-分配, revoke-撤销
     */
    private String operation;
}
