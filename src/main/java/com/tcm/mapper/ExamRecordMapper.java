package com.tcm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.dto.ExamLeaderboardDTO;
import com.tcm.entity.ExamRecord;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface ExamRecordMapper extends BaseMapper<ExamRecord> {

    @Select("SELECT * FROM exam_record WHERE 1=1 " +
            "<if test='examRecord.userId != null'> AND user_id = #{examRecord.userId} </if>" +
            "<if test='examRecord.status != null'> AND status = #{examRecord.status} </if>")
    List<ExamRecord> selectAll(@Param("examRecord") ExamRecord examRecord);

    @Select("SELECT COUNT(DISTINCT user_id) FROM exam_record " +
            "WHERE create_time >= #{startTime} AND create_time < #{endTime} " +
            "AND status = 'finished'")
    long countUsersBetween(@Param("startTime") LocalDateTime startTime,
                           @Param("endTime") LocalDateTime endTime);

    @Select("SELECT COALESCE(AVG(total_score), 0) FROM exam_record " +
            "WHERE create_time >= #{startTime} AND create_time < #{endTime} " +
            "AND status = 'finished'")
    double getAverageScoreBetween(@Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime);

    @Select("SELECT t.score, u.username, u.name, u.avatar " +
            "FROM (" +
            "   SELECT user_id, MAX(total_score) as score " +
            "   FROM exam_record " +
            "   WHERE status = 'finished' " +
            "   GROUP BY user_id" +
            ") t " +
            "LEFT JOIN user u ON t.user_id = u.id " +
            "ORDER BY t.score DESC " +
            "LIMIT #{limit}")
    List<ExamLeaderboardDTO> selectTopScoreUsers(@Param("limit") int limit);
} 