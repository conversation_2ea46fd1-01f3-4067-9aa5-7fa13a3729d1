package com.tcm.mapper;

import com.tcm.entity.Course;
import com.tcm.entity.CourseTypeStat;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface CourseMapper {

    void insert(Course course);

    int deleteById(Integer id);

    int updateById(Course course);

    Course selectById(Integer id);

    List<Course> selectAll(Course course);


    @Select("select * from course where recommend = '是' and type = #{type}")
    Course getRecommend(String type);

    @Select("select * from course where recommend = '否' and type = #{type} order by id desc limit 8")
    List<Course> selectTop8(String type);

    @Select("SELECT COUNT(*) FROM course")
    long count();

    @Select("SELECT COUNT(*) FROM course_order WHERE status = 'paid'")
    long getTotalOrders();

    @Select("SELECT * FROM course ORDER BY create_time DESC LIMIT #{limit}")
    List<Course> selectLatest(int limit);

    @Select("SELECT type as typeName, COUNT(*) as count FROM course GROUP BY type")
    List<CourseTypeStat> selectCourseTypeStats();
}