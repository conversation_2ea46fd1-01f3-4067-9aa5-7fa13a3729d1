package com.tcm.mapper;

import com.tcm.entity.Information;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface InformationMapper {

    void insert(Information information);

    int deleteById(Integer id);

    int updateById(Information information);

    Information selectById(Integer id);

    List<Information> selectAll(Information information);

    /**
     * 查询推荐
     */
    @Select("select * from information where recommend = '是' and status = '审核通过'")
    Information selectRecommend();

    /**
     * 查询最新的8条资料
     */
    @Select("select * from information where recommend = '否' and status = '审核通过' order by id desc limit 8")
    List<Information> selectTop8();

    @Select("SELECT COUNT(*) FROM information")
    long count();

    @Select("SELECT COUNT(*) FROM information WHERE status = #{status}")
    long countByStatus(String status);
}