package com.tcm.mapper;

import com.tcm.entity.Signin;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface SigninMapper {

    /**
     * 新增
     */
    int insert(Signin signin);

    /**
     * 删除
     */
    int deleteById(Integer id);

    /**
     * 修改
     */
    int updateById(Signin signin);

    /**
     * 根据ID查询
     */
    Signin selectById(Integer id);

    /**
     * 查询所有
     */
    List<Signin> selectAll(Signin signin);

    @Select("select * from signin where user_id = #{userId}")
    Signin selectByUserId(Integer userId);

    @Select("select * from signin where user_id = #{userId} and day = #{day}")
    Signin selectByUserIdAndDay(@Param("userId") Integer userId, @Param("day") String day);
}