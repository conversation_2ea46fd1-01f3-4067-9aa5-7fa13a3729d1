package com.tcm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色Mapper接口
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role> {

    /**
     * 根据用户ID和用户类型查询用户角色
     */
    @Select("SELECT r.* FROM roles r " +
            "INNER JOIN user_roles ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND ur.user_type = #{userType} AND r.status = 1")
    List<Role> selectRolesByUser(@Param("userId") Integer userId, @Param("userType") String userType);

    /**
     * 根据角色编码查询角色
     */
    @Select("SELECT * FROM roles WHERE role_code = #{roleCode} AND status = 1")
    Role selectByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 查询所有启用的角色
     */
    @Select("SELECT * FROM roles WHERE status = 1 ORDER BY id")
    List<Role> selectAllEnabled();
}
