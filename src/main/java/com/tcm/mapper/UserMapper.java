package com.tcm.mapper;

import com.tcm.entity.User;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface UserMapper {
    @Select("SELECT * FROM user WHERE username = #{username}")
    User selectByUserName(String username);

    @Insert("INSERT INTO user(username, password, name, avatar, role, phone, email, member, score, account, create_time, update_time) " +
            "VALUES(#{username}, #{password}, #{name}, #{avatar}, #{role}, #{phone}, #{email}, #{member}, #{score}, #{account}, #{createTime}, #{updateTime})")
    void insert(User user);

    @Delete("DELETE FROM user WHERE id = #{id}")
    int deleteById(Integer id);

    @Update("UPDATE user SET username=#{username}, password=#{password}, name=#{name}, " +
            "avatar=#{avatar}, role=#{role}, phone=#{phone}, email=#{email}, " +
            "member=#{member}, score=#{score}, account=#{account}, update_time=#{updateTime} " +
            "WHERE id=#{id}")
    int updateById(User user);

    @Select("SELECT * FROM user WHERE id = #{id}")
    User selectById(Integer id);

    @Select("<script>" +
            "SELECT * FROM user WHERE 1=1" +
            "<if test='user.username != null'> AND username LIKE CONCAT('%', #{user.username}, '%')</if>" +
            "<if test='user.name != null'> AND name LIKE CONCAT('%', #{user.name}, '%')</if>" +
            "<if test='user.role != null'> AND role = #{user.role}</if>" +
            "</script>")
    List<User> selectAll(@Param("user") User user);

    @Select("SELECT COUNT(*) FROM user")
    long count();

    @Select("SELECT COUNT(*) FROM user WHERE create_time >= #{startTime}")
    long countByCreateTimeAfter(@Param("startTime") LocalDateTime startTime);

    @Select("SELECT COUNT(DISTINCT user_id) FROM (" +
            "SELECT user_id FROM exam_record WHERE create_time BETWEEN #{start} AND #{end} " +
            "UNION " +
            "SELECT user_id FROM course_order WHERE create_time BETWEEN #{start} AND #{end} " +
            "UNION " +
            "SELECT user_id FROM user_login_log WHERE login_time BETWEEN #{start} AND #{end}" +
            ") AS active_users")
    long countActiveUsersBetween(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end);
}