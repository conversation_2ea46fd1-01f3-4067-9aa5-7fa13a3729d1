package com.tcm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 课程测试记录实体类
 */
@Data
@TableName("course_test_record")
public class CourseTestRecord {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 课程ID
     */
    private Integer courseId;
    
    /**
     * 得分
     */
    private Integer score;
    
    /**
     * 用户答案，逗号分隔
     */
    private String answers;
    
    /**
     * 题目ID，逗号分隔
     */
    private String questionIds;
    
    /**
     * 创建时间
     */
    private String createTime;
} 