package com.tcm.entity;

import java.io.Serializable;
import lombok.Data;

@Data
public class AnswerRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id; // 答题记录的唯一标识
    private String userName; // 用户名
    private Integer testId; // 题目ID
    private String selectedOption; // 用户选择的选项
    private Integer correct; // 用户的答案是否正确
    private String answerDate; // 答题时间
}
