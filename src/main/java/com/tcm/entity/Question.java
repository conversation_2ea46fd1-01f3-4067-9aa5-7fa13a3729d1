package com.tcm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.handler.JsonListTypeHandler;
import com.tcm.handler.JsonTypeHandler;
import com.tcm.handler.MapTypeHandler;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.Data;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@Data
@TableName("exam_question")
public class Question {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String type;
    private String content;

    @TableField(value = "options", typeHandler = JsonTypeHandler.class)
    private Map<String, String> options;
    private String caseText;
    @TableField(value = "sub_questions", typeHandler = JsonListTypeHandler.class)
    private List<Map<String, String>> subQuestions;
    private String correctAnswer;
    private Integer score;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String examId;
}