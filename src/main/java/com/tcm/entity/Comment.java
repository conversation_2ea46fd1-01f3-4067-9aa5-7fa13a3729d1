package com.tcm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 课程评论实体类
 */
@Data
@TableName("comment")
public class Comment {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 评论内容
     */
    private String content;
    
    /**
     * 评论时间
     */
    private String createTime;
    
    /**
     * 评论用户ID
     */
    private Integer userId;
    
    /**
     * 课程ID
     */
    private Integer courseId;
    
    /**
     * 父评论ID，为空则为顶级评论
     */
    private Integer parentId;
    
    /**
     * 评论状态：正常、隐藏
     */
    private String status;
    
    /**
     * 评论用户名，非数据库字段
     */
    @TableField(exist = false)
    private String userName;
    
    /**
     * 用户头像，非数据库字段
     */
    @TableField(exist = false)
    private String userAvatar;
} 