package com.tcm.service;

import com.github.pagehelper.PageInfo;
import com.tcm.dto.ExamLeaderboardDTO;
import com.tcm.entity.ExamRecord;
import java.util.List;

public interface ExamRecordService {
    void add(ExamRecord examRecord);

    void deleteById(Integer id);

    void deleteBatch(List<Integer> ids);

    void updateById(ExamRecord examRecord);

    ExamRecord selectById(Integer id);

    List<ExamRecord> selectAll(ExamRecord examRecord);

    PageInfo<ExamRecord> selectPage(ExamRecord examRecord, Integer pageNum, Integer pageSize);

    // 统计今日答题人数
    long countTodayExamUsers();

    // 获取今日平均分
    double getTodayAverageScore();

    // 获取得分最高的用户记录
    List<ExamLeaderboardDTO> findTopScoreUsers(int limit);
} 