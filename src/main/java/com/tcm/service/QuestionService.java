package com.tcm.service;

import java.util.List;
import java.util.Map;
import org.springframework.web.multipart.MultipartFile;

import com.tcm.entity.Question;

public interface QuestionService {
    Map<String, Object> getQuestionList(Long examId, String type, Integer page, Integer size);

    void addQuestion(Map<String, Object> params);

    void updateQuestion(Map<String, Object> params);

    void deleteQuestion(Long id);

    void importQuestions(MultipartFile file, Long examId, String type);
    
    /**
     * 随机获取指定类型的题目
     * @param type 题目类型
     * @param count 题目数量
     * @return 随机题目列表
     */
    List<Question> getRandomQuestionsByType(String type, Integer count);
} 