package com.tcm.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.tcm.entity.UserLoginLog;
import com.tcm.mapper.UserLoginLogMapper;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

/**
 * 异步日志服务
 */
@Slf4j
@Service
public class AsyncLogService {

    @Autowired
    private UserLoginLogMapper userLoginLogMapper;

    /**
     * 异步记录用户登录日志
     * @param userId 用户ID
     * @param ipAddress IP地址
     * @param deviceInfo 设备信息
     * @return CompletableFuture<Void>
     */
    @Async("logExecutor")
    public CompletableFuture<Void> logUserLogin(Integer userId, String ipAddress, String deviceInfo) {
        try {
            UserLoginLog loginLog = new UserLoginLog();
            loginLog.setUserId(userId);
            loginLog.setIpAddress(ipAddress);
            loginLog.setDeviceInfo(deviceInfo);
            loginLog.setLoginTime(LocalDateTime.now());

            userLoginLogMapper.insert(loginLog);

            log.debug("用户登录: ID={}, IP={}", userId, ipAddress);
            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("记录登录日志失败，用户ID: {}", userId, e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 异步记录操作日志
     * @param userId 用户ID
     * @param operation 操作类型
     * @param description 操作描述
     * @param ipAddress IP地址
     * @return CompletableFuture<Void>
     */
    @Async("logExecutor")
    public CompletableFuture<Void> logUserOperation(Integer userId, String operation, String description, String ipAddress) {
        try {
            // 简化的操作日志记录
            log.info("用户操作: ID={}, 操作={}, 描述={}, IP={}",
                    userId, operation, description, ipAddress);

            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("记录操作日志失败，用户ID: {}, 操作: {}", userId, operation, e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 异步记录系统错误日志
     * @param errorType 错误类型
     * @param errorMessage 错误消息
     * @param stackTrace 堆栈跟踪
     * @param userId 用户ID（可选）
     * @return CompletableFuture<Void>
     */
    @Async("logExecutor")
    public CompletableFuture<Void> logSystemError(String errorType, String errorMessage, String stackTrace, Integer userId) {
        try {
            log.error("系统错误: 类型={}, 消息={}, 用户ID={}",
                    errorType, errorMessage, userId);

            // 详细堆栈信息只在DEBUG级别记录
            log.debug("错误堆栈: {}", stackTrace);

            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("记录系统错误日志失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 异步记录性能监控日志
     * @param methodName 方法名
     * @param executionTime 执行时间（毫秒）
     * @param parameters 参数信息
     * @return CompletableFuture<Void>
     */
    @Async("logExecutor")
    public CompletableFuture<Void> logPerformance(String methodName, long executionTime, String parameters) {
        try {
            // 只记录执行时间超过阈值的方法
            if (executionTime > 1000) { // 超过1秒的方法
                log.warn("慢方法: {} 耗时{}ms", methodName, executionTime);
                log.debug("慢方法参数: {}", parameters);
            } else {
                log.debug("方法: {} 耗时{}ms", methodName, executionTime);
            }

            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("记录性能监控日志失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }
}
