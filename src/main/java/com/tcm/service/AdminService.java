package com.tcm.service;

import com.github.pagehelper.PageInfo;
import com.tcm.entity.Admin;
import java.util.List;

public interface AdminService {

    void deleteById(Integer id);

    void updateById(Admin admin);

    Admin selectById(Integer id);

    List<Admin> selectAll(Admin admin);

    void add(Admin admin);

    void deleteBatch(List<Integer> ids);

    PageInfo<Admin> selectPage(Admin admin, Integer pageNum, Integer pageSize);
}
