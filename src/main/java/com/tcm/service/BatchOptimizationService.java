package com.tcm.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 批量操作优化服务
 * 提供高性能的批量数据库操作
 */
@Slf4j
@Service
public class BatchOptimizationService {

    @Autowired
    private CacheService cacheService;

    /**
     * 优化的批量删除操作
     * 使用事务和批处理提升性能
     */
    @Async("batchExecutor")
    @Transactional(rollbackFor = Exception.class)
    public <T> CompletableFuture<Void> optimizedBatchDelete(
            List<T> ids, 
            Consumer<List<T>> batchDeleteFunction, 
            String entityName,
            String cacheType) {
        
        try {
            log.info("开始优化批量删除{}，数量: {}", entityName, ids.size());
            long startTime = System.currentTimeMillis();
            
            if (ids.isEmpty()) {
                return CompletableFuture.completedFuture(null);
            }
            
            // 分批处理，每批100个（优化后的批次大小）
            int batchSize = 100;
            int totalBatches = (ids.size() + batchSize - 1) / batchSize;
            
            for (int i = 0; i < totalBatches; i++) {
                int start = i * batchSize;
                int end = Math.min(start + batchSize, ids.size());
                List<T> batch = ids.subList(start, end);
                
                log.debug("处理第{}/{}批，数量: {}", i + 1, totalBatches, batch.size());
                
                // 执行批量删除
                batchDeleteFunction.accept(batch);
                
                // 清理相关缓存
                if (cacheType != null) {
                    batch.forEach(id -> cacheService.evict(cacheType, id.toString()));
                }
            }
            
            long endTime = System.currentTimeMillis();
            log.info("优化批量删除{}完成，总耗时: {}ms", entityName, endTime - startTime);
            
            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("优化批量删除{}失败", entityName, e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 优化的批量插入操作
     */
    @Async("batchExecutor")
    @Transactional(rollbackFor = Exception.class)
    public <T> CompletableFuture<Void> optimizedBatchInsert(
            List<T> items, 
            Consumer<List<T>> batchInsertFunction, 
            String entityName) {
        
        try {
            log.info("开始优化批量插入{}，数量: {}", entityName, items.size());
            long startTime = System.currentTimeMillis();
            
            if (items.isEmpty()) {
                return CompletableFuture.completedFuture(null);
            }
            
            // 分批处理，每批100个
            int batchSize = 100;
            int totalBatches = (items.size() + batchSize - 1) / batchSize;
            
            for (int i = 0; i < totalBatches; i++) {
                int start = i * batchSize;
                int end = Math.min(start + batchSize, items.size());
                List<T> batch = items.subList(start, end);
                
                log.debug("处理第{}/{}批，数量: {}", i + 1, totalBatches, batch.size());
                
                // 执行批量插入
                batchInsertFunction.accept(batch);
            }
            
            long endTime = System.currentTimeMillis();
            log.info("优化批量插入{}完成，总耗时: {}ms", entityName, endTime - startTime);
            
            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("优化批量插入{}失败", entityName, e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 优化的批量更新操作
     */
    @Async("batchExecutor")
    @Transactional(rollbackFor = Exception.class)
    public <T> CompletableFuture<Void> optimizedBatchUpdate(
            List<T> items, 
            Consumer<List<T>> batchUpdateFunction, 
            String entityName,
            String cacheType) {
        
        try {
            log.info("开始优化批量更新{}，数量: {}", entityName, items.size());
            long startTime = System.currentTimeMillis();
            
            if (items.isEmpty()) {
                return CompletableFuture.completedFuture(null);
            }
            
            // 分批处理，每批100个
            int batchSize = 100;
            int totalBatches = (items.size() + batchSize - 1) / batchSize;
            
            for (int i = 0; i < totalBatches; i++) {
                int start = i * batchSize;
                int end = Math.min(start + batchSize, items.size());
                List<T> batch = items.subList(start, end);
                
                log.debug("处理第{}/{}批，数量: {}", i + 1, totalBatches, batch.size());
                
                // 执行批量更新
                batchUpdateFunction.accept(batch);
                
                // 清理相关缓存
                if (cacheType != null) {
                    // 这里需要根据实际情况获取ID，暂时跳过缓存清理
                    // 可以在具体实现中添加ID提取逻辑
                }
            }
            
            long endTime = System.currentTimeMillis();
            log.info("优化批量更新{}完成，总耗时: {}ms", entityName, endTime - startTime);
            
            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("优化批量更新{}失败", entityName, e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 并行处理大数据集
     */
    @Async("batchExecutor")
    public <T, R> CompletableFuture<List<R>> parallelProcess(
            List<T> items, 
            java.util.function.Function<T, R> processor, 
            String operationName) {
        
        try {
            log.info("开始并行处理{}，数量: {}", operationName, items.size());
            long startTime = System.currentTimeMillis();
            
            // 使用并行流处理
            List<R> results = items.parallelStream()
                    .map(processor)
                    .collect(Collectors.toList());
            
            long endTime = System.currentTimeMillis();
            log.info("并行处理{}完成，总耗时: {}ms", operationName, endTime - startTime);
            
            return CompletableFuture.completedFuture(results);
        } catch (Exception e) {
            log.error("并行处理{}失败", operationName, e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 分页批量处理
     * 适用于超大数据集的处理
     */
    @Async("batchExecutor")
    public <T> CompletableFuture<Void> paginatedBatchProcess(
            int totalCount,
            int pageSize,
            java.util.function.Function<Integer, List<T>> dataLoader,
            Consumer<List<T>> processor,
            String operationName) {
        
        try {
            log.info("开始分页批量处理{}，总数量: {}", operationName, totalCount);
            long startTime = System.currentTimeMillis();
            
            int totalPages = (totalCount + pageSize - 1) / pageSize;
            
            for (int page = 0; page < totalPages; page++) {
                log.debug("处理第{}/{}页", page + 1, totalPages);
                
                // 加载当前页数据
                List<T> pageData = dataLoader.apply(page);
                
                if (!pageData.isEmpty()) {
                    // 处理当前页数据
                    processor.accept(pageData);
                }
                
                // 避免内存占用过高，可以添加短暂休眠
                if (page % 10 == 0 && page > 0) {
                    Thread.sleep(10); // 每10页休眠10ms
                }
            }
            
            long endTime = System.currentTimeMillis();
            log.info("分页批量处理{}完成，总耗时: {}ms", operationName, endTime - startTime);
            
            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("分页批量处理{}失败", operationName, e);
            return CompletableFuture.failedFuture(e);
        }
    }
}
