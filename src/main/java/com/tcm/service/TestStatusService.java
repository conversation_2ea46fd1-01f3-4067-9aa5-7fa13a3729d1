package com.tcm.service;

import com.github.pagehelper.PageInfo;
import com.tcm.entity.UserTestStatus;
import java.util.List;

public interface TestStatusService {
    void add(UserTestStatus userTestStatus);

    void deleteById(Integer id);

    void deleteBatch(List<Integer> ids);

    void updateById(UserTestStatus userTestStatus);

    UserTestStatus selectById(Integer id);

    List<UserTestStatus> selectAll(UserTestStatus userTestStatus);

    PageInfo<UserTestStatus> selectPage(UserTestStatus userTestStatus, Integer pageNum, Integer pageSize);

    UserTestStatus findByUserIdAndTestId(Integer id, Integer id1);

}
