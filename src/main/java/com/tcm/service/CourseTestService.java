package com.tcm.service;

import com.tcm.entity.CourseTestRecord;

import java.util.List;
import java.util.Map;

/**
 * 课程测试服务接口
 */
public interface CourseTestService {
    
    /**
     * 保存测试记录
     * @param record 测试记录
     * @return 保存结果
     */
    boolean saveTestRecord(CourseTestRecord record);
    
    /**
     * 获取用户的测试记录
     * @param userId 用户ID
     * @param courseId 课程ID（可选）
     * @return 测试记录列表
     */
    List<CourseTestRecord> getUserTestRecords(Integer userId, Integer courseId);
    
    /**
     * 计算并保存测试分数
     * @param params 测试参数（包含用户答案和正确答案等信息）
     * @return 计算结果
     */
    Map<String, Object> calculateTestScore(Map<String, Object> params);
} 