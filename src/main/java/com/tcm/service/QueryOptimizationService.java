package com.tcm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 数据库查询优化服务
 * 提供高性能的数据库查询方法
 */
@Slf4j
@Service
public class QueryOptimizationService {

    @Autowired
    private CacheService cacheService;

    /**
     * 优化的分页查询（带缓存）
     */
    public <T> Page<T> optimizedPageQuery(
            String cacheKey,
            String cacheType,
            Supplier<Page<T>> queryFunction,
            int page,
            int size) {
        
        try {
            // 构建缓存键
            String fullCacheKey = String.format("%s_page_%d_size_%d", cacheKey, page, size);
            
            // 从缓存获取或执行查询
            return cacheService.getQuestion(fullCacheKey, queryFunction);
            
        } catch (Exception e) {
            log.error("优化分页查询失败，直接执行查询: {}", cacheKey, e);
            return queryFunction.get();
        }
    }

    /**
     * 优化的列表查询（带缓存）
     */
    public <T> List<T> optimizedListQuery(
            String cacheKey,
            String cacheType,
            Supplier<List<T>> queryFunction) {
        
        try {
            // 根据缓存类型选择合适的缓存策略
            switch (cacheType) {
                case "stats":
                    return cacheService.getStats(cacheKey, queryFunction);
                case "user":
                    return cacheService.getUser(cacheKey, queryFunction);
                case "question":
                    return cacheService.getQuestion(cacheKey, queryFunction);
                case "exam":
                    return cacheService.getExam(cacheKey, queryFunction);
                default:
                    return cacheService.getQuestion(cacheKey, queryFunction);
            }
            
        } catch (Exception e) {
            log.error("优化列表查询失败，直接执行查询: {}", cacheKey, e);
            return queryFunction.get();
        }
    }

    /**
     * 优化的单个对象查询（带缓存）
     */
    public <T> T optimizedSingleQuery(
            String cacheKey,
            String cacheType,
            Supplier<T> queryFunction) {
        
        try {
            // 根据缓存类型选择合适的缓存策略
            switch (cacheType) {
                case "user":
                    return cacheService.getUser(cacheKey, queryFunction);
                case "question":
                    return cacheService.getQuestion(cacheKey, queryFunction);
                case "exam":
                    return cacheService.getExam(cacheKey, queryFunction);
                default:
                    return cacheService.getQuestion(cacheKey, queryFunction);
            }
            
        } catch (Exception e) {
            log.error("优化单个查询失败，直接执行查询: {}", cacheKey, e);
            return queryFunction.get();
        }
    }

    /**
     * 异步并行查询
     */
    public <T> CompletableFuture<List<T>> asyncParallelQuery(
            List<Supplier<T>> queryFunctions,
            String operationName) {
        
        try {
            log.debug("开始异步并行查询: {}", operationName);
            long startTime = System.currentTimeMillis();
            
            // 创建异步任务
            List<CompletableFuture<T>> futures = queryFunctions.stream()
                    .map(CompletableFuture::supplyAsync)
                    .toList();
            
            // 等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0]));
            
            return allFutures.thenApply(v -> {
                List<T> results = futures.stream()
                        .map(CompletableFuture::join)
                        .toList();
                
                long endTime = System.currentTimeMillis();
                log.debug("异步并行查询完成: {}，耗时: {}ms", operationName, endTime - startTime);
                
                return results;
            });
            
        } catch (Exception e) {
            log.error("异步并行查询失败: {}", operationName, e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 批量查询优化
     * 使用IN查询替代多次单个查询
     */
    public <T, ID> Map<ID, T> optimizedBatchQuery(
            List<ID> ids,
            Function<List<ID>, List<T>> batchQueryFunction,
            Function<T, ID> idExtractor,
            String cacheType,
            String entityName) {
        
        try {
            log.debug("开始批量查询{}，数量: {}", entityName, ids.size());
            long startTime = System.currentTimeMillis();
            
            // 构建缓存键
            String cacheKey = String.format("batch_%s_%s", entityName, 
                    String.join("_", ids.stream().map(String::valueOf).toList()));
            
            // 从缓存获取或执行批量查询
            List<T> results = optimizedListQuery(cacheKey, cacheType, 
                    () -> batchQueryFunction.apply(ids));
            
            // 转换为Map便于查找
            Map<ID, T> resultMap = results.stream()
                    .collect(java.util.stream.Collectors.toMap(idExtractor, Function.identity()));
            
            long endTime = System.currentTimeMillis();
            log.debug("批量查询{}完成，耗时: {}ms", entityName, endTime - startTime);
            
            return resultMap;
            
        } catch (Exception e) {
            log.error("批量查询{}失败", entityName, e);
            throw new RuntimeException("批量查询失败: " + e.getMessage());
        }
    }

    /**
     * 游标分页查询（适用于大数据量）
     */
    public <T> List<T> cursorBasedQuery(
            Object lastCursor,
            int limit,
            Function<Object, LambdaQueryWrapper<T>> queryWrapperBuilder,
            Function<List<T>, List<T>> queryExecutor,
            String entityName) {
        
        try {
            log.debug("开始游标分页查询{}，游标: {}, 限制: {}", entityName, lastCursor, limit);
            long startTime = System.currentTimeMillis();
            
            // 构建查询条件
            LambdaQueryWrapper<T> wrapper = queryWrapperBuilder.apply(lastCursor);
            
            // 执行查询
            List<T> results = queryExecutor.apply(List.of()).stream()
                    .limit(limit)
                    .toList();
            
            long endTime = System.currentTimeMillis();
            log.debug("游标分页查询{}完成，返回: {}条，耗时: {}ms", 
                    entityName, results.size(), endTime - startTime);
            
            return results;
            
        } catch (Exception e) {
            log.error("游标分页查询{}失败", entityName, e);
            throw new RuntimeException("游标分页查询失败: " + e.getMessage());
        }
    }

    /**
     * 预加载关联数据
     * 解决N+1查询问题
     */
    public <T, R> void preloadAssociations(
            List<T> entities,
            Function<T, Object> foreignKeyExtractor,
            Function<List<Object>, Map<Object, R>> associationLoader,
            java.util.function.BiConsumer<T, R> associationSetter,
            String associationName) {
        
        try {
            if (entities.isEmpty()) {
                return;
            }
            
            log.debug("开始预加载关联数据: {}，实体数量: {}", associationName, entities.size());
            long startTime = System.currentTimeMillis();
            
            // 提取外键
            List<Object> foreignKeys = entities.stream()
                    .map(foreignKeyExtractor)
                    .filter(java.util.Objects::nonNull)
                    .distinct()
                    .toList();
            
            if (foreignKeys.isEmpty()) {
                return;
            }
            
            // 批量加载关联数据
            Map<Object, R> associationMap = associationLoader.apply(foreignKeys);
            
            // 设置关联数据
            entities.forEach(entity -> {
                Object foreignKey = foreignKeyExtractor.apply(entity);
                if (foreignKey != null && associationMap.containsKey(foreignKey)) {
                    associationSetter.accept(entity, associationMap.get(foreignKey));
                }
            });
            
            long endTime = System.currentTimeMillis();
            log.debug("预加载关联数据{}完成，耗时: {}ms", associationName, endTime - startTime);
            
        } catch (Exception e) {
            log.error("预加载关联数据{}失败", associationName, e);
        }
    }
}
