package com.tcm.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * 异步统计服务
 */
@Slf4j
@Service
public class AsyncStatsService {

    @Autowired
    private UserService userService;

    @Autowired
    private CourseService courseService;

    @Autowired
    private InformationService informationService;

    @Autowired
    private ExamRecordService examRecordService;

    @Autowired
    private CacheService cacheService;

    /**
     * 异步获取用户统计数据（带缓存）
     */
    @Async("statsExecutor")
    public CompletableFuture<UserStats> getUserStats() {
        try {
            log.debug("开始获取用户统计数据");
            long startTime = System.currentTimeMillis();

            // 使用缓存获取数据
            UserStats stats = cacheService.getStats("user_stats", () -> {
                long totalUsers = userService.count();
                long newUsers = userService.countNewUsersToday();
                return new UserStats(totalUsers, newUsers);
            });

            log.debug("用户统计数据获取完成，耗时: {}ms", System.currentTimeMillis() - startTime);
            return CompletableFuture.completedFuture(stats);
        } catch (Exception e) {
            log.error("获取用户统计数据失败", e);
            return CompletableFuture.completedFuture(new UserStats(0L, 0L));
        }
    }

    /**
     * 异步获取课程统计数据（带缓存）
     */
    @Async("statsExecutor")
    public CompletableFuture<CourseStats> getCourseStats() {
        try {
            log.debug("开始获取课程统计数据");
            long startTime = System.currentTimeMillis();

            // 使用缓存获取数据
            CourseStats stats = cacheService.getStats("course_stats", () -> {
                long courseCount = courseService.count();
                long courseViews = courseService.getTotalViews();
                return new CourseStats(courseCount, courseViews);
            });

            log.debug("课程统计数据获取完成，耗时: {}ms", System.currentTimeMillis() - startTime);
            return CompletableFuture.completedFuture(stats);
        } catch (Exception e) {
            log.error("获取课程统计数据失败", e);
            return CompletableFuture.completedFuture(new CourseStats(0L, 0L));
        }
    }

    /**
     * 异步获取资料统计数据
     */
    @Async("statsExecutor")
    public CompletableFuture<ResourceStats> getResourceStats() {
        try {
            log.debug("开始获取资料统计数据");
            long startTime = System.currentTimeMillis();
            
            long resourceCount = informationService.count();
            long pendingReview = informationService.countPendingReview();
            
            ResourceStats stats = new ResourceStats(resourceCount, pendingReview);
            
            log.debug("资料统计数据获取完成，耗时: {}ms", System.currentTimeMillis() - startTime);
            return CompletableFuture.completedFuture(stats);
        } catch (Exception e) {
            log.error("获取资料统计数据失败", e);
            return CompletableFuture.completedFuture(new ResourceStats(0L, 0L));
        }
    }

    /**
     * 异步获取考试统计数据
     */
    @Async("statsExecutor")
    public CompletableFuture<ExamStats> getExamStats() {
        try {
            log.debug("开始获取考试统计数据");
            long startTime = System.currentTimeMillis();
            
            long todayExamUsers = examRecordService.countTodayExamUsers();
            double avgScore = examRecordService.getTodayAverageScore();
            
            ExamStats stats = new ExamStats(todayExamUsers, avgScore);
            
            log.debug("考试统计数据获取完成，耗时: {}ms", System.currentTimeMillis() - startTime);
            return CompletableFuture.completedFuture(stats);
        } catch (Exception e) {
            log.error("获取考试统计数据失败", e);
            return CompletableFuture.completedFuture(new ExamStats(0L, 0.0));
        }
    }

    // 内部类定义统计数据结构
    public static class UserStats {
        private final long totalUsers;
        private final long newUsers;

        public UserStats(long totalUsers, long newUsers) {
            this.totalUsers = totalUsers;
            this.newUsers = newUsers;
        }

        public long getTotalUsers() { return totalUsers; }
        public long getNewUsers() { return newUsers; }
    }

    public static class CourseStats {
        private final long courseCount;
        private final long courseViews;

        public CourseStats(long courseCount, long courseViews) {
            this.courseCount = courseCount;
            this.courseViews = courseViews;
        }

        public long getCourseCount() { return courseCount; }
        public long getCourseViews() { return courseViews; }
    }

    public static class ResourceStats {
        private final long resourceCount;
        private final long pendingReview;

        public ResourceStats(long resourceCount, long pendingReview) {
            this.resourceCount = resourceCount;
            this.pendingReview = pendingReview;
        }

        public long getResourceCount() { return resourceCount; }
        public long getPendingReview() { return pendingReview; }
    }

    public static class ExamStats {
        private final long todayExamUsers;
        private final double avgScore;

        public ExamStats(long todayExamUsers, double avgScore) {
            this.todayExamUsers = todayExamUsers;
            this.avgScore = avgScore;
        }

        public long getTodayExamUsers() { return todayExamUsers; }
        public double getAvgScore() { return avgScore; }
    }
}
