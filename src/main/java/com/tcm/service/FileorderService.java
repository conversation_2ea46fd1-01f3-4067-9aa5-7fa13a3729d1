package com.tcm.service;

import com.github.pagehelper.PageInfo;
import com.tcm.entity.Fileorder;
import java.util.List;

public interface FileorderService {
    void add(Fileorder fileorder);

    void deleteById(Integer id);

    void deleteBatch(List<Integer> ids);

    void updateById(Fileorder fileorder);

    Fileorder selectById(Integer id);

    List<Fileorder> selectAll(Fileorder fileorder);

    PageInfo<Fileorder> selectPage(Fileorder fileorder, Integer pageNum, Integer pageSize);
}
