package com.tcm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.entity.Question;
import com.tcm.mapper.QuestionMapper;
import com.tcm.service.QuestionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class QuestionServiceImpl extends ServiceImpl<QuestionMapper, Question> implements QuestionService {

    @Override
    public Map<String, Object> getQuestionList(Long examId, String type, Integer page, Integer size) {
        // 构建查询条件
        LambdaQueryWrapper<Question> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Question::getExamId, examId.toString())
                .eq(Question::getType, type)
                .orderByDesc(Question::getCreateTime);

        // 分页查询
        Page<Question> questionPage = new Page<>(page, size);
        Page<Question> result = this.page(questionPage, wrapper);

        // 封装返回结果
        Map<String, Object> map = new HashMap<>();
        map.put("records", result.getRecords());
        map.put("total", result.getTotal());
        return map;
    }

    @Override
    public void addQuestion(Map<String, Object> params) {
        Question question = new Question();
        question.setExamId(params.get("examId").toString());
        question.setType(params.get("type").toString());
        question.setContent(params.get("content").toString());

        // 设置选项(单选、多选题)
        if (params.get("options") != null) {
            question.setOptions((Map<String, String>) params.get("options"));
        }

        // 设置病例描述(病例分析题)
        if (params.get("caseText") != null) {
            question.setCaseText(params.get("caseText").toString());
        }

        question.setCorrectAnswer(params.get("correctAnswer").toString());

        // 设置分值
        String type = params.get("type").toString();
        int score = "essay".equals(type) ? 15 : "case".equals(type) ? 20 : 5;
        question.setScore(score);

        this.save(question);
    }

    @Override
    public void updateQuestion(Map<String, Object> params) {
        Question question = this.getById(params.get("id").toString());
        if (question == null) {
            throw new RuntimeException("题目不存在");
        }

        question.setContent(params.get("content").toString());

        if (params.get("options") != null) {
            question.setOptions((Map<String, String>) params.get("options"));
        }

        if (params.get("caseText") != null) {
            question.setCaseText(params.get("caseText").toString());
        }

        question.setCorrectAnswer(params.get("correctAnswer").toString());

        this.updateById(question);
    }

    @Override
    public void deleteQuestion(Long id) {
        this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importQuestions(MultipartFile file, Long examId, String type) {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("上传文件为空");
        }

        // 校验文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.endsWith(".xlsx")) {
            throw new RuntimeException("请上传Excel文件(xlsx格式)");
        }

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            // 读取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null || sheet.getLastRowNum() < 1) {
                throw new RuntimeException("Excel文件内容为空或格式不正确");
            }

            // 存储解析的题目
            List<Question> questionList = new ArrayList<>();

            // 从第二行开始读取数据（第一行是表头）
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                try {
                    Question question = new Question();
                    // 注意：不要手动设置ID，让数据库自动生成
                    question.setExamId(examId.toString());
                    question.setType(type);

                    // 读取题目内容
                    Cell contentCell = row.getCell(0);
                    if (contentCell == null) {
                        continue; // 跳过没有题目内容的行
                    }
                    String content = getCellStringValue(contentCell);
                    if (content == null || content.trim().isEmpty()) {
                        continue; // 跳过题目内容为空的行
                    }
                    question.setContent(content);

                    // 根据题目类型处理不同的数据
                    switch (type) {
                        case "single":
                        case "multiple":
                            // 读取选项A-F
                            Map<String, String> options = new HashMap<>();
                            for (int j = 0; j < 6; j++) { // 最多支持6个选项A-F
                                Cell optionCell = row.getCell(j + 1);
                                if (optionCell != null) {
                                    String optionValue = getCellStringValue(optionCell);
                                    if (optionValue != null && !optionValue.trim().isEmpty()) {
                                        char optionKey = (char) ('A' + j);
                                        options.put(String.valueOf(optionKey), optionValue);
                                    }
                                }
                            }

                            // 检查选项数量
                            if (options.size() < 2) {
                                continue; // 至少需要两个选项
                            }
                            question.setOptions(options);

                            // 读取正确答案
                            Cell answerCell = row.getCell(7);
                            if (answerCell != null) {
                                String answer = getCellStringValue(answerCell);
                                if (answer != null && !answer.trim().isEmpty()) {
                                    question.setCorrectAnswer(answer.trim().toUpperCase());
                                } else {
                                    continue; // 跳过没有正确答案的行
                                }
                            } else {
                                continue; // 跳过没有正确答案的行
                            }
                            break;

                        case "judge":
                            // 读取正确答案（true/false）
                            Cell judgeAnswerCell = row.getCell(1);
                            if (judgeAnswerCell != null) {
                                String answer = getCellStringValue(judgeAnswerCell);
                                if (answer != null && !answer.trim().isEmpty()) {
                                    // 标准化答案格式
                                    if ("是".equals(answer) || "正确".equals(answer) || "对".equals(answer) || "1".equals(answer)) {
                                        answer = "true";
                                    } else if ("否".equals(answer) || "错误".equals(answer) || "错".equals(answer) || "0".equals(answer)) {
                                        answer = "false";
                                    }
                                    question.setCorrectAnswer(answer);
                                } else {
                                    continue; // 跳过没有正确答案的行
                                }
                            } else {
                                continue; // 跳过没有正确答案的行
                            }
                            break;

                        case "essay":
                            // 读取参考答案
                            Cell essayAnswerCell = row.getCell(1);
                            if (essayAnswerCell != null) {
                                String answer = getCellStringValue(essayAnswerCell);
                                if (answer != null && !answer.trim().isEmpty()) {
                                    question.setCorrectAnswer(answer);
                                } else {
                                    continue; // 跳过没有参考答案的行
                                }
                            } else {
                                continue; // 跳过没有参考答案的行
                            }
                            break;

                        case "case":
                            // 读取病例描述
                            Cell caseTextCell = row.getCell(1);
                            if (caseTextCell != null) {
                                String caseText = getCellStringValue(caseTextCell);
                                if (caseText != null && !caseText.trim().isEmpty()) {
                                    question.setCaseText(caseText);

                                    // 从Excel中读取子问题，而不是设置默认值
                                    // 尝试读取第3列和第4列作为子问题
                                    List<Map<String, String>> subQuestions = new ArrayList<>();

                                    // 读取第一个子问题
                                    Cell subQuestion1Cell = row.getCell(2);
                                    if (subQuestion1Cell != null) {
                                        String subQ1 = getCellStringValue(subQuestion1Cell);
                                        if (subQ1 != null && !subQ1.trim().isEmpty()) {
                                            Map<String, String> subQuestion1 = new HashMap<>();
                                            subQuestion1.put("question", subQ1);
                                            subQuestions.add(subQuestion1);
                                        }
                                    }

                                    // 读取第二个子问题
                                    Cell subQuestion2Cell = row.getCell(3);
                                    if (subQuestion2Cell != null) {
                                        String subQ2 = getCellStringValue(subQuestion2Cell);
                                        if (subQ2 != null && !subQ2.trim().isEmpty()) {
                                            Map<String, String> subQuestion2 = new HashMap<>();
                                            subQuestion2.put("question", subQ2);
                                            subQuestions.add(subQuestion2);
                                        }
                                    }

                                    // 如果没有找到子问题，设置默认子问题
                                    if (subQuestions.isEmpty()) {
                                        Map<String, String> defaultSubQ1 = new HashMap<>();
                                        defaultSubQ1.put("question", "请给出最可能的诊断");
                                        subQuestions.add(defaultSubQ1);

                                        Map<String, String> defaultSubQ2 = new HashMap<>();
                                        defaultSubQ2.put("question", "列出急诊处理措施");
                                        subQuestions.add(defaultSubQ2);
                                    }

                                    question.setSubQuestions(subQuestions);
                                } else {
                                    continue; // 跳过没有病例描述的行
                                }
                            } else {
                                continue; // 跳过没有病例描述的行
                            }

                            // 读取参考答案
                            Cell caseAnswerCell = row.getCell(4);
                            if (caseAnswerCell != null) {
                                String answer = getCellStringValue(caseAnswerCell);
                                if (answer != null && !answer.trim().isEmpty()) {
                                    question.setCorrectAnswer(answer);
                                } else {
                                    continue; // 跳过没有参考答案的行
                                }
                            } else {
                                continue; // 跳过没有参考答案的行
                            }
                            break;
                        default:
                            throw new RuntimeException("不支持的题目类型: " + type);
                    }

                    // 设置分值
                    int score = "essay".equals(type) ? 15 : "case".equals(type) ? 20 : 5;
                    question.setScore(score);

                    // 设置创建时间和更新时间
                    LocalDateTime now = LocalDateTime.now();
                    question.setCreateTime(now);
                    question.setUpdateTime(now);

                    questionList.add(question);
                } catch (Exception e) {
                    // 记录该行解析异常但继续处理其他行
                    log.warn("第{}行数据解析异常: {}", i + 1, e.getMessage());
                }
            }

            // 批量保存题目
            if (!questionList.isEmpty()) {
                this.saveBatch(questionList);
                log.info("成功导入{}道题目", questionList.size());
            } else {
                throw new RuntimeException("未解析到有效题目数据");
            }

        } catch (IOException e) {
            throw new RuntimeException("解析Excel文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取单元格的字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 防止数字被转成科学计数法
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue();
                } catch (Exception e) {
                    return String.valueOf(cell.getNumericCellValue());
                }
            default:
                return "";
        }
    }

    @Override
    public List<Question> getRandomQuestionsByType(String type, Integer count) {
        try {
            // 先检查该类型题目的总数
            long totalCount = baseMapper.countByType(type);

            if (totalCount == 0) {
                return new ArrayList<>();
            }

            // 如果请求的数量大于等于总数，直接返回所有题目
            if (count >= totalCount) {
                LambdaQueryWrapper<Question> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(Question::getType, type);
                return this.list(wrapper);
            }

            // 使用数据库层面的随机查询，避免加载所有数据到内存
            return baseMapper.selectRandomByType(type, count);

        } catch (Exception e) {
            log.error("获取随机题目失败，类型: {}, 数量: {}", type, count, e);
            // 降级处理：使用原有方式
            LambdaQueryWrapper<Question> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Question::getType, type);
            List<Question> allQuestions = this.list(wrapper);
            Collections.shuffle(allQuestions);
            return allQuestions.stream().limit(count).collect(Collectors.toList());
        }
    }
} 