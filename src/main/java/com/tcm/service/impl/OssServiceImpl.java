package com.tcm.service.impl;

import cn.hutool.core.lang.UUID;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.PutObjectRequest;
import com.tcm.service.OssService;
import java.io.InputStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class OssServiceImpl implements OssService {

    @Autowired
    private OSS ossClient;

    @Value("${aliyun.oss.bucket}")
    private String bucketName;

    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    public String uploadFile(MultipartFile file) {
        try {
            // 获取文件名并修改它
            String originalFileName = file.getOriginalFilename();
            String fileExtension = "";
            if (originalFileName != null && originalFileName.contains(".")) {
                fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
            }
            // 使用UUID（或者其他方法）生成新的文件名
            String newFileName = UUID.randomUUID().toString() + fileExtension;

            // 获取上传的文件流
            InputStream inputStream = file.getInputStream();

            // 创建PutObjectRequest对象
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, newFileName, inputStream);

            // 上传文件到OSS
            ossClient.putObject(putObjectRequest);

            // 如果Bucket是公共读，你可以直接生成URL
            return "https://" + bucketName + "." + endpoint + "/" + newFileName;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}
