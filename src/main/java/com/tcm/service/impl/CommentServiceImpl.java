package com.tcm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.entity.Comment;
import com.tcm.entity.User;
import com.tcm.mapper.CommentMapper;
import com.tcm.mapper.UserMapper;
import com.tcm.service.CommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 评论服务实现类
 */
@Service
public class CommentServiceImpl extends ServiceImpl<CommentMapper, Comment> implements CommentService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public boolean add(Comment comment) {
        // 设置默认状态为正常
        comment.setStatus("正常");
        return save(comment);
    }

    @Override
    public List<Comment> listByCourseId(Integer courseId) {
        // 根据课程ID获取评论列表
        LambdaQueryWrapper<Comment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Comment::getCourseId, courseId);
        wrapper.orderByDesc(Comment::getCreateTime); // 按创建时间倒序排序
        
        List<Comment> comments = list(wrapper);
        
        // 填充用户信息
        for (Comment comment : comments) {
            User user = userMapper.selectById(comment.getUserId());
            if (user != null) {
                comment.setUserName(user.getName());
                comment.setUserAvatar(user.getAvatar());
            }
        }
        
        return comments;
    }

    @Override
    public boolean deleteById(Integer id) {
        return removeById(id);
    }
} 