package com.tcm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tcm.entity.UserTestStatus;
import com.tcm.mapper.TestStatusMapper;
import com.tcm.service.TestStatusService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TestStatusServiceImpl implements TestStatusService {

    @Autowired
    private TestStatusMapper testStatusMapper;

    public void add(UserTestStatus userTestStatus) {
        testStatusMapper.insert(userTestStatus);
    }

    public void deleteById(Integer id) {
        testStatusMapper.deleteById(id);
    }

    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            testStatusMapper.deleteById(id);
        }
    }

    public void updateById(UserTestStatus userTestStatus) {
        testStatusMapper.insert(userTestStatus);
    }


    public UserTestStatus selectById(Integer id) {
        return testStatusMapper.selectById(id);
    }


    public List<UserTestStatus> selectAll(UserTestStatus userTestStatus) {
        return testStatusMapper.selectAll(userTestStatus);
    }


    public PageInfo<UserTestStatus> selectPage(UserTestStatus userTestStatus, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<UserTestStatus> tests = selectAll(userTestStatus);
        return new PageInfo<>(tests);
    }

    public UserTestStatus findByUserIdAndTestId(Integer userId, Integer testId) {
        return testStatusMapper.findByUserIdAndTestId(userId, testId);
    }

}
