package com.tcm.service.impl;

import com.tcm.dto.PermissionTreeDTO;
import com.tcm.entity.Permission;
import com.tcm.entity.Role;
import com.tcm.mapper.PermissionMapper;
import com.tcm.mapper.RoleMapper;
import com.tcm.mapper.RolePermissionMapper;
import com.tcm.mapper.UserRoleMapper;
import com.tcm.service.PermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 权限管理服务实现类
 */
@Slf4j
@Service
public class PermissionServiceImpl implements PermissionService {

    @Autowired
    private PermissionMapper permissionMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private RolePermissionMapper rolePermissionMapper;

    @Override
    @Cacheable(value = "userPermissions", key = "#userId + '_' + #userType")
    public List<Permission> getUserPermissions(Integer userId, String userType) {
        try {
            return permissionMapper.selectPermissionsByUser(userId, userType);
        } catch (Exception e) {
            log.error("获取用户权限失败，userId: {}, userType: {}", userId, userType, e);
            return List.of();
        }
    }

    @Override
    @Cacheable(value = "userRoles", key = "#userId + '_' + #userType")
    public List<Role> getUserRoles(Integer userId, String userType) {
        try {
            return roleMapper.selectRolesByUser(userId, userType);
        } catch (Exception e) {
            log.error("获取用户角色失败，userId: {}, userType: {}", userId, userType, e);
            return List.of();
        }
    }

    @Override
    public boolean hasPermission(Integer userId, String userType, String permissionCode) {
        try {
            // 先检查是否为超级管理员
            if ("admin".equals(userType)) {
                List<Role> roles = getUserRoles(userId, userType);
                boolean isSuperAdmin = roles.stream()
                        .anyMatch(r -> "SUPER_ADMIN".equals(r.getRoleCode()));
                if (isSuperAdmin) {
                    log.debug("超级管理员用户 {} 拥有权限: {}", userId, permissionCode);
                    return true;
                }
            }

            List<Permission> permissions = getUserPermissions(userId, userType);
            boolean hasPermission = permissions.stream()
                    .anyMatch(p -> permissionCode.equals(p.getPermissionCode()));

            log.debug("用户 {} (类型: {}) 权限检查 {}: {}, 总权限数: {}",
                     userId, userType, permissionCode, hasPermission, permissions.size());

            return hasPermission;
        } catch (Exception e) {
            log.error("检查用户权限失败，userId: {}, userType: {}, permission: {}",
                     userId, userType, permissionCode, e);
            return false;
        }
    }

    @Override
    public boolean hasRole(Integer userId, String userType, String roleCode) {
        try {
            List<Role> roles = getUserRoles(userId, userType);

            // 超级管理员拥有所有角色权限
            boolean isSuperAdmin = roles.stream()
                    .anyMatch(r -> "SUPER_ADMIN".equals(r.getRoleCode()));
            if (isSuperAdmin) {
                log.debug("超级管理员用户 {} 拥有角色: {}", userId, roleCode);
                return true;
            }

            boolean hasRole = roles.stream()
                    .anyMatch(r -> roleCode.equals(r.getRoleCode()));

            log.debug("用户 {} (类型: {}) 角色检查 {}: {}, 总角色数: {}",
                     userId, userType, roleCode, hasRole, roles.size());

            return hasRole;
        } catch (Exception e) {
            log.error("检查用户角色失败，userId: {}, userType: {}, role: {}",
                     userId, userType, roleCode, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean assignRole(Integer userId, String userType, String roleCode) {
        try {
            Role role = roleMapper.selectByRoleCode(roleCode);
            if (role == null) {
                log.warn("角色不存在: {}", roleCode);
                return false;
            }

            // 检查是否已经拥有该角色
            if (userRoleMapper.countUserRole(userId, userType, role.getId()) > 0) {
                log.info("用户已拥有角色，userId: {}, userType: {}, role: {}", userId, userType, roleCode);
                return true;
            }

            int result = userRoleMapper.insertUserRole(userId, userType, role.getId());
            if (result > 0) {
                log.info("用户角色分配成功，userId: {}, userType: {}, role: {}", userId, userType, roleCode);
                // 清除缓存
                clearUserCache(userId, userType);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("分配用户角色失败，userId: {}, userType: {}, role: {}", 
                     userId, userType, roleCode, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean removeRole(Integer userId, String userType, String roleCode) {
        try {
            Role role = roleMapper.selectByRoleCode(roleCode);
            if (role == null) {
                log.warn("角色不存在: {}", roleCode);
                return false;
            }

            int result = userRoleMapper.deleteUserRole(userId, userType, role.getId());
            if (result > 0) {
                log.info("用户角色移除成功，userId: {}, userType: {}, role: {}", userId, userType, roleCode);
                // 清除缓存
                clearUserCache(userId, userType);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("移除用户角色失败，userId: {}, userType: {}, role: {}", 
                     userId, userType, roleCode, e);
            return false;
        }
    }

    @Override
    @Cacheable(value = "allPermissions")
    public List<Permission> getAllPermissions() {
        try {
            return permissionMapper.selectAllEnabled();
        } catch (Exception e) {
            log.error("获取所有权限失败", e);
            return List.of();
        }
    }

    @Override
    @Cacheable(value = "allRoles")
    public List<Role> getAllRoles() {
        try {
            return roleMapper.selectAllEnabled();
        } catch (Exception e) {
            log.error("获取所有角色失败", e);
            return List.of();
        }
    }

    @Override
    @Cacheable(value = "permissionsByCategory", key = "#category")
    public List<Permission> getPermissionsByCategory(String category) {
        try {
            return permissionMapper.selectByCategory(category);
        } catch (Exception e) {
            log.error("根据分类获取权限失败，category: {}", category, e);
            return List.of();
        }
    }

    @Override
    @Cacheable(value = "permissionsByRole", key = "#roleCode")
    public List<Permission> getPermissionsByRole(String roleCode) {
        try {
            Role role = roleMapper.selectByRoleCode(roleCode);
            if (role == null) {
                return List.of();
            }
            return permissionMapper.selectPermissionsByRoleId(role.getId());
        } catch (Exception e) {
            log.error("根据角色获取权限失败，roleCode: {}", roleCode, e);
            return List.of();
        }
    }

    @Override
    @Transactional
    public void initUserDefaultRole(Integer userId, String userType) {
        try {
            // 检查用户是否已有角色
            List<Role> existingRoles = getUserRoles(userId, userType);
            if (!existingRoles.isEmpty()) {
                log.info("用户已有角色，跳过初始化，userId: {}, userType: {}", userId, userType);
                return;
            }

            // 根据用户类型分配默认角色
            String defaultRoleCode;
            if ("admin".equals(userType)) {
                defaultRoleCode = "ADMIN"; // 管理员默认角色
            } else if ("student".equals(userType)) {
                defaultRoleCode = "STUDENT"; // 学生默认角色
            } else {
                defaultRoleCode = "USER"; // 兼容原有逻辑
            }

            boolean success = assignRole(userId, userType, defaultRoleCode);
            if (success) {
                log.info("用户默认角色初始化成功，userId: {}, userType: {}, role: {}", 
                        userId, userType, defaultRoleCode);
            } else {
                log.error("用户默认角色初始化失败，userId: {}, userType: {}", userId, userType);
            }
        } catch (Exception e) {
            log.error("初始化用户默认角色失败，userId: {}, userType: {}", userId, userType, e);
        }
    }

    @Override
    @Transactional
    public boolean assignRolesToUser(Integer userId, String userType, List<Integer> roleIds) {
        try {
            for (Integer roleId : roleIds) {
                // 检查角色是否存在
                Role role = roleMapper.selectById(roleId);
                if (role == null) {
                    log.warn("角色不存在: {}", roleId);
                    continue;
                }

                // 检查是否已经拥有该角色
                if (userRoleMapper.countUserRole(userId, userType, roleId) == 0) {
                    userRoleMapper.insertUserRole(userId, userType, roleId);
                    log.info("用户角色分配成功，userId: {}, userType: {}, roleId: {}", userId, userType, roleId);
                }
            }

            // 清除缓存
            clearUserCache(userId, userType);
            return true;
        } catch (Exception e) {
            log.error("批量分配用户角色失败，userId: {}, userType: {}, roleIds: {}",
                     userId, userType, roleIds, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean revokeRolesFromUser(Integer userId, String userType, List<Integer> roleIds) {
        try {
            for (Integer roleId : roleIds) {
                userRoleMapper.deleteUserRole(userId, userType, roleId);
                log.info("用户角色移除成功，userId: {}, userType: {}, roleId: {}", userId, userType, roleId);
            }

            // 清除缓存
            clearUserCache(userId, userType);
            return true;
        } catch (Exception e) {
            log.error("批量移除用户角色失败，userId: {}, userType: {}, roleIds: {}",
                     userId, userType, roleIds, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean assignPermissionsToRole(Integer roleId, List<Integer> permissionIds) {
        try {
            for (Integer permissionId : permissionIds) {
                // 检查权限是否存在
                Permission permission = permissionMapper.selectById(permissionId);
                if (permission == null) {
                    log.warn("权限不存在: {}", permissionId);
                    continue;
                }

                // 检查角色是否已有该权限
                // 这里需要添加一个查询方法
                // 暂时直接插入，数据库唯一约束会处理重复
                try {
                    rolePermissionMapper.insert(roleId, permissionId);
                    log.info("角色权限分配成功，roleId: {}, permissionId: {}", roleId, permissionId);
                } catch (Exception ignored) {
                    // 忽略重复插入异常
                }
            }

            return true;
        } catch (Exception e) {
            log.error("批量分配角色权限失败，roleId: {}, permissionIds: {}", roleId, permissionIds, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean revokePermissionsFromRole(Integer roleId, List<Integer> permissionIds) {
        try {
            for (Integer permissionId : permissionIds) {
                rolePermissionMapper.delete(roleId, permissionId);
                log.info("角色权限移除成功，roleId: {}, permissionId: {}", roleId, permissionId);
            }

            return true;
        } catch (Exception e) {
            log.error("批量移除角色权限失败，roleId: {}, permissionIds: {}", roleId, permissionIds, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean replaceRolePermissions(Integer roleId, List<Integer> permissionIds) {
        try {
            // 先删除角色的所有权限
            rolePermissionMapper.deleteByRoleId(roleId);

            // 再分配新权限
            return assignPermissionsToRole(roleId, permissionIds);
        } catch (Exception e) {
            log.error("替换角色权限失败，roleId: {}, permissionIds: {}", roleId, permissionIds, e);
            return false;
        }
    }

    @Override
    public List<PermissionTreeDTO> getPermissionTree() {
        try {
            List<Permission> allPermissions = getAllPermissions();
            return buildPermissionTree(allPermissions, null);
        } catch (Exception e) {
            log.error("获取权限树失败", e);
            return List.of();
        }
    }

    @Override
    public List<PermissionTreeDTO> getRolePermissionTree(Integer roleId) {
        try {
            List<Permission> allPermissions = getAllPermissions();
            List<Permission> rolePermissions = getPermissionsByRoleId(roleId);
            Set<Integer> assignedPermissionIds = rolePermissions.stream()
                    .map(Permission::getId)
                    .collect(Collectors.toSet());

            return buildPermissionTree(allPermissions, assignedPermissionIds);
        } catch (Exception e) {
            log.error("获取角色权限树失败，roleId: {}", roleId, e);
            return List.of();
        }
    }

    @Override
    public Map<String, Object> getUserDetailInfo(Integer userId, String userType) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 获取用户角色
            List<Role> userRoles = getUserRoles(userId, userType);
            result.put("roles", userRoles);

            // 获取用户权限
            List<Permission> userPermissions = getUserPermissions(userId, userType);
            result.put("permissions", userPermissions);

            // 获取权限树（标记已分配的权限）
            Set<Integer> assignedPermissionIds = userPermissions.stream()
                    .map(Permission::getId)
                    .collect(Collectors.toSet());
            List<Permission> allPermissions = getAllPermissions();
            List<PermissionTreeDTO> permissionTree = buildPermissionTree(allPermissions, assignedPermissionIds);
            result.put("permissionTree", permissionTree);

            return result;
        } catch (Exception e) {
            log.error("获取用户详细信息失败，userId: {}, userType: {}", userId, userType, e);
            return new HashMap<>();
        }
    }

    /**
     * 根据角色ID获取权限列表
     */
    private List<Permission> getPermissionsByRoleId(Integer roleId) {
        try {
            return permissionMapper.selectPermissionsByRoleId(roleId);
        } catch (Exception e) {
            log.error("根据角色ID获取权限失败，roleId: {}", roleId, e);
            return List.of();
        }
    }

    /**
     * 构建权限树结构
     */
    private List<PermissionTreeDTO> buildPermissionTree(List<Permission> permissions, Set<Integer> assignedPermissionIds) {
        Map<String, List<Permission>> categoryMap = permissions.stream()
                .collect(Collectors.groupingBy(Permission::getCategory));

        return categoryMap.entrySet().stream()
                .map(entry -> {
                    PermissionTreeDTO treeNode = new PermissionTreeDTO();
                    treeNode.setCategory(entry.getKey());

                    List<PermissionTreeDTO.PermissionNodeDTO> nodes = entry.getValue().stream()
                            .map(permission -> {
                                PermissionTreeDTO.PermissionNodeDTO node = new PermissionTreeDTO.PermissionNodeDTO();
                                node.setId(permission.getId());
                                node.setPermissionCode(permission.getPermissionCode());
                                node.setPermissionName(permission.getPermissionName());
                                node.setDescription(permission.getDescription());
                                node.setAssigned(assignedPermissionIds != null && assignedPermissionIds.contains(permission.getId()));
                                return node;
                            })
                            .collect(Collectors.toList());

                    treeNode.setPermissions(nodes);
                    return treeNode;
                })
                .sorted(Comparator.comparing(PermissionTreeDTO::getCategory))
                .collect(Collectors.toList());
    }

    /**
     * 清除用户相关缓存
     */
    private void clearUserCache(Integer userId, String userType) {
        // 这里可以使用Spring Cache的CacheManager来清除特定缓存
        // 暂时使用日志记录
        log.debug("清除用户缓存，userId: {}, userType: {}", userId, userType);
    }
}
