package com.tcm.service.impl;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tcm.common.enums.MemberEnum;
import com.tcm.common.enums.ResultCodeEnum;
import com.tcm.common.utils.TokenUtils;
import com.tcm.entity.Account;
import com.tcm.entity.Course;
import com.tcm.entity.Orders;
import com.tcm.entity.User;
import com.tcm.exception.CustomException;
import com.tcm.mapper.CourseMapper;
import com.tcm.mapper.OrdersMapper;
import com.tcm.mapper.UserMapper;
import com.tcm.service.OrdersService;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OrdersServiceImpl implements OrdersService {

    @Autowired
    private OrdersMapper ordersMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private CourseMapper courseMapper;

    /**
     * 新增
     */
    public void add(Orders orders) {
        orders.setTime(DateUtil.now());
        orders.setOrderId(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        Account currentUser = TokenUtils.getCurrentUser();
        User user = userMapper.selectById(currentUser.getId());
        Course course = courseMapper.selectById(orders.getCourseId());
        Double price = course.getPrice();
        if (MemberEnum.yes.info.equals(user.getMember())) {
            price = course.getPrice() * course.getDiscount();
        }
        if (user.getAccount() < price) {
            throw new CustomException(ResultCodeEnum.ACCOUNT_LOWER_ERROR);
        }
        orders.setPrice(price);
        ordersMapper.insert(orders);
        user.setAccount(user.getAccount() - price);
        userMapper.updateById(user);
    }

    /**
     * 删除
     */
    public void deleteById(Integer id) {
        ordersMapper.deleteById(id);
    }

    /**
     * 批量删除
     */
    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            ordersMapper.deleteById(id);
        }
    }

    /**
     * 修改
     */
    public void updateById(Orders orders) {
        ordersMapper.updateById(orders);
    }

    /**
     * 根据ID查询
     */
    public Orders selectById(Integer id) {
        return ordersMapper.selectById(id);
    }

    /**
     * 查询所有
     */
    public List<Orders> selectAll(Orders orders) {
        return ordersMapper.selectAll(orders);
    }

    /**
     * 分页查询
     */
    public PageInfo<Orders> selectPage(Orders orders, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Orders> list = ordersMapper.selectAll(orders);
        return PageInfo.of(list);
    }

}