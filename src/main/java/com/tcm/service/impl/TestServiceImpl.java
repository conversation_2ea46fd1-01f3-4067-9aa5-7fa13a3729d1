package com.tcm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tcm.entity.Test;
import com.tcm.mapper.TestMapper;
import com.tcm.service.TestService;
import com.tcm.service.UserService;
import java.util.List;
import java.util.Random;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TestServiceImpl implements TestService {

    @Autowired
    private TestMapper testMapper;

    @Autowired
    private UserService userService;

    public void add(Test test) {
        testMapper.insert(test);
    }

    public void deleteById(Integer id) {
        testMapper.deleteById(id);
    }

    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            testMapper.deleteById(id);
        }
    }

    public void updateById(Test test) {
        testMapper.insert(test);
    }


    public Test selectById(Integer id) {
        return testMapper.selectById(id);
    }


    public List<Test> selectAll(Test test) {
        return testMapper.selectAll(test);
    }


    public PageInfo<Test> selectPage(Test test, String status, Integer pageNum, Integer pageSize) {
        Integer currentUserId = userService.getCurrentUser().getId();
        PageHelper.startPage(pageNum, pageSize);
        List<Test> list = testMapper.selectByPageAndStatus(test, currentUserId, status, pageNum, pageSize);
        return new PageInfo<>(list);
    }


    public Test selectRandomTestForUser(Integer userId) {
        Test testCriteria = new Test();
        PageHelper.startPage(1, Integer.MAX_VALUE);

        List<Test> tests = testMapper.selectByPageAndStatus(testCriteria, userId, "未完成", 1, Integer.MAX_VALUE);
        PageInfo<Test> pageInfo = new PageInfo<>(tests);

        if (pageInfo.getList().isEmpty()) {
            return null;
        }
        return pageInfo.getList().get(new Random().nextInt(pageInfo.getList().size()));
    }


}
