package com.tcm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tcm.entity.Information;
import com.tcm.mapper.InformationMapper;
import com.tcm.service.InformationService;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class InformationServiceImpl implements InformationService {

    @Resource
    private InformationMapper informationMapper;

    @Override
    public void add(Information information) {
        information.setCreateTime(LocalDateTime.now().toString());
        informationMapper.insert(information);
    }

    @Override
    public void deleteById(Integer id) {
        informationMapper.deleteById(id);
    }

    @Override
    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            informationMapper.deleteById(id);
        }
    }

    @Override
    public void updateById(Information information) {
        informationMapper.updateById(information);
    }

    @Override
    public Information selectById(Integer id) {
        return informationMapper.selectById(id);
    }

    @Override
    public List<Information> selectAll(Information information) {
        return informationMapper.selectAll(information);
    }

    @Override
    public PageInfo<Information> selectPage(Information information, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Information> list = informationMapper.selectAll(information);
        return PageInfo.of(list);
    }

    @Override
    public Information getRecommend() {
        return informationMapper.selectRecommend();
    }

    @Override
    public List<Information> selectTop8() {
        return informationMapper.selectTop8();
    }

    @Override
    public long count() {
        return informationMapper.count();
    }

    @Override
    public long countPendingReview() {
        return informationMapper.countByStatus("pending");
    }
}