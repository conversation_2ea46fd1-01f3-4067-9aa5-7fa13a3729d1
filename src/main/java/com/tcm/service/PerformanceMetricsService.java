package com.tcm.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.ThreadMXBean;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能指标收集服务
 * 收集和监控系统性能指标
 */
@Slf4j
@Service
public class PerformanceMetricsService {

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private CacheService cacheService;

    // 性能计数器
    private final ConcurrentHashMap<String, AtomicLong> performanceCounters = new ConcurrentHashMap<>();
    
    // 响应时间统计
    private final ConcurrentHashMap<String, Timer> responseTimers = new ConcurrentHashMap<>();
    
    // 错误计数器
    private final ConcurrentHashMap<String, Counter> errorCounters = new ConcurrentHashMap<>();

    /**
     * 记录方法执行时间
     */
    public void recordExecutionTime(String methodName, long executionTime) {
        Timer timer = responseTimers.computeIfAbsent(methodName, 
            name -> Timer.builder("method.execution.time")
                    .tag("method", name)
                    .register(meterRegistry));
        
        timer.record(executionTime, java.util.concurrent.TimeUnit.MILLISECONDS);
        
        // 记录慢方法
        if (executionTime > 1000) {
            incrementCounter("slow.methods", methodName);
        }
    }

    /**
     * 增加计数器
     */
    public void incrementCounter(String counterName, String tag) {
        performanceCounters.computeIfAbsent(counterName + ":" + tag, 
            k -> new AtomicLong(0)).incrementAndGet();
        
        Counter counter = errorCounters.computeIfAbsent(counterName + ":" + tag,
            name -> Counter.builder(counterName)
                    .tag("type", tag)
                    .register(meterRegistry));
        counter.increment();
    }

    /**
     * 记录数据库查询性能
     */
    public void recordDatabaseQuery(String queryType, long executionTime, boolean fromCache) {
        Timer timer = Timer.builder("database.query.time")
                .tag("type", queryType)
                .tag("cached", String.valueOf(fromCache))
                .register(meterRegistry);
        
        timer.record(executionTime, java.util.concurrent.TimeUnit.MILLISECONDS);
        
        // 记录缓存命中率
        if (fromCache) {
            incrementCounter("cache.hits", queryType);
        } else {
            incrementCounter("cache.misses", queryType);
        }
    }

    /**
     * 记录API调用
     */
    public void recordApiCall(String endpoint, String method, int statusCode, long responseTime) {
        Timer timer = Timer.builder("api.response.time")
                .tag("endpoint", endpoint)
                .tag("method", method)
                .tag("status", String.valueOf(statusCode))
                .register(meterRegistry);
        
        timer.record(responseTime, java.util.concurrent.TimeUnit.MILLISECONDS);
        
        // 记录错误
        if (statusCode >= 400) {
            incrementCounter("api.errors", endpoint);
        }
    }

    /**
     * 记录线程池使用情况
     */
    public void recordThreadPoolUsage(String poolName, int activeThreads, int queueSize) {
        meterRegistry.gauge("threadpool.active.threads", 
                java.util.Collections.singletonList(io.micrometer.core.instrument.Tag.of("pool", poolName)), 
                activeThreads);
        
        meterRegistry.gauge("threadpool.queue.size", 
                java.util.Collections.singletonList(io.micrometer.core.instrument.Tag.of("pool", poolName)), 
                queueSize);
    }

    /**
     * 定期收集系统指标
     */
    @Scheduled(fixedRate = 30000) // 每30秒执行一次
    public void collectSystemMetrics() {
        try {
            // 收集内存使用情况
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
            
            meterRegistry.gauge("system.memory.used", usedMemory);
            meterRegistry.gauge("system.memory.max", maxMemory);
            meterRegistry.gauge("system.memory.usage.percent", 
                    maxMemory > 0 ? (double) usedMemory / maxMemory * 100 : 0);

            // 收集线程使用情况
            ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
            int threadCount = threadBean.getThreadCount();
            int peakThreadCount = threadBean.getPeakThreadCount();
            
            meterRegistry.gauge("system.threads.count", threadCount);
            meterRegistry.gauge("system.threads.peak", peakThreadCount);

            // 收集缓存统计
            collectCacheMetrics();
            
            log.debug("系统指标收集完成 - 内存使用: {}MB/{MB, 线程数: {}", 
                    usedMemory / 1024 / 1024, maxMemory / 1024 / 1024, threadCount);

        } catch (Exception e) {
            log.error("收集系统指标失败", e);
        }
    }

    /**
     * 收集缓存指标
     */
    private void collectCacheMetrics() {
        try {
            String cacheStats = cacheService.getCacheStats();
            log.debug("缓存统计信息: {}", cacheStats);
            
            // 这里可以解析缓存统计信息并记录到指标中
            // 由于Caffeine的stats()返回的是复杂对象，这里简化处理
            
        } catch (Exception e) {
            log.error("收集缓存指标失败", e);
        }
    }

    /**
     * 获取性能报告
     */
    public String getPerformanceReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 性能指标报告 ===\n");
        
        // 计数器统计
        report.append("\n--- 计数器统计 ---\n");
        performanceCounters.forEach((key, value) -> 
            report.append(String.format("%s: %d\n", key, value.get())));
        
        // 响应时间统计
        report.append("\n--- 响应时间统计 ---\n");
        responseTimers.forEach((key, timer) -> {
            report.append(String.format("%s: 总数=%d, 总时间=%.2fms\n",
                    key, timer.count(), timer.totalTime(java.util.concurrent.TimeUnit.MILLISECONDS)));
        });
        
        // 系统资源统计
        report.append("\n--- 系统资源 ---\n");
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
        long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
        
        report.append(String.format("内存使用: %dMB / %dMB (%.1f%%)\n", 
                usedMemory / 1024 / 1024, maxMemory / 1024 / 1024,
                maxMemory > 0 ? (double) usedMemory / maxMemory * 100 : 0));
        
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        report.append(String.format("线程数: %d (峰值: %d)\n", 
                threadBean.getThreadCount(), threadBean.getPeakThreadCount()));
        
        return report.toString();
    }

    /**
     * 重置性能计数器
     */
    public void resetCounters() {
        performanceCounters.clear();
        responseTimers.clear();
        log.info("性能计数器已重置");
    }

    /**
     * 检查性能阈值并告警
     */
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void checkPerformanceThresholds() {
        try {
            // 检查内存使用率
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
            double memoryUsagePercent = maxMemory > 0 ? (double) usedMemory / maxMemory * 100 : 0;
            
            if (memoryUsagePercent > 80) {
                log.warn("内存使用率过高: {:.1f}%", memoryUsagePercent);
                incrementCounter("performance.alerts", "high_memory_usage");
            }
            
            // 检查慢方法数量
            AtomicLong slowMethods = performanceCounters.get("slow.methods:total");
            if (slowMethods != null && slowMethods.get() > 10) {
                log.warn("慢方法数量过多: {}", slowMethods.get());
                incrementCounter("performance.alerts", "too_many_slow_methods");
            }
            
        } catch (Exception e) {
            log.error("性能阈值检查失败", e);
        }
    }
}
