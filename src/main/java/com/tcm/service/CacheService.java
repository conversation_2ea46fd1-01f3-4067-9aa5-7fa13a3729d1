package com.tcm.service;

import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 多级缓存服务
 * 实现L1(Caffeine) + L2(Redis)的缓存策略
 */
@Slf4j
@Service
public class CacheService {

    @Autowired
    @Qualifier("statsCache")
    private Cache<String, Object> statsCache;

    @Autowired
    @Qualifier("userCache")
    private Cache<String, Object> userCache;

    @Autowired
    @Qualifier("questionCache")
    private Cache<String, Object> questionCache;

    @Autowired
    @Qualifier("examCache")
    private Cache<String, Object> examCache;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取统计数据（短期缓存）
     */
    public <T> T getStats(String key, Supplier<T> dataLoader) {
        return getFromCache(statsCache, "stats:" + key, dataLoader, 1, TimeUnit.MINUTES);
    }

    /**
     * 获取用户数据（中期缓存）
     */
    public <T> T getUser(String key, Supplier<T> dataLoader) {
        return getFromCache(userCache, "user:" + key, dataLoader, 10, TimeUnit.MINUTES);
    }

    /**
     * 获取题目数据（长期缓存）
     */
    public <T> T getQuestion(String key, Supplier<T> dataLoader) {
        return getFromCache(questionCache, "question:" + key, dataLoader, 1, TimeUnit.HOURS);
    }

    /**
     * 获取考试数据（长期缓存）
     */
    public <T> T getExam(String key, Supplier<T> dataLoader) {
        return getFromCache(examCache, "exam:" + key, dataLoader, 2, TimeUnit.HOURS);
    }

    /**
     * 多级缓存获取数据
     * L1: Caffeine本地缓存
     * L2: Redis分布式缓存
     * L3: 数据库查询
     */
    @SuppressWarnings("unchecked")
    private <T> T getFromCache(Cache<String, Object> localCache, String key, 
                              Supplier<T> dataLoader, long timeout, TimeUnit timeUnit) {
        try {
            // L1: 尝试从本地缓存获取
            Object cachedValue = localCache.getIfPresent(key);
            if (cachedValue != null) {
                log.debug("L1缓存命中: {}", key);
                return (T) cachedValue;
            }

            // L2: 尝试从Redis获取
            cachedValue = redisTemplate.opsForValue().get(key);
            if (cachedValue != null) {
                log.debug("L2缓存命中: {}", key);
                // 回填到L1缓存
                localCache.put(key, cachedValue);
                return (T) cachedValue;
            }

            // L3: 从数据源加载
            log.debug("缓存未命中，从数据源加载: {}", key);
            T data = dataLoader.get();
            if (data != null) {
                // 同时写入L1和L2缓存
                localCache.put(key, data);
                redisTemplate.opsForValue().set(key, data, timeout, timeUnit);
                log.debug("数据已缓存: {}", key);
            }
            return data;

        } catch (Exception e) {
            log.error("缓存操作失败，直接从数据源加载: {}", key, e);
            return dataLoader.get();
        }
    }

    /**
     * 删除缓存
     */
    public void evict(String cacheType, String key) {
        String fullKey = cacheType + ":" + key;
        try {
            // 删除L1缓存
            switch (cacheType) {
                case "stats":
                    statsCache.invalidate(fullKey);
                    break;
                case "user":
                    userCache.invalidate(fullKey);
                    break;
                case "question":
                    questionCache.invalidate(fullKey);
                    break;
                case "exam":
                    examCache.invalidate(fullKey);
                    break;
            }

            // 删除L2缓存
            redisTemplate.delete(fullKey);
            log.debug("缓存已删除: {}", fullKey);

        } catch (Exception e) {
            log.error("删除缓存失败: {}", fullKey, e);
        }
    }

    /**
     * 清空指定类型的所有缓存
     */
    public void evictAll(String cacheType) {
        try {
            // 清空L1缓存
            switch (cacheType) {
                case "stats":
                    statsCache.invalidateAll();
                    break;
                case "user":
                    userCache.invalidateAll();
                    break;
                case "question":
                    questionCache.invalidateAll();
                    break;
                case "exam":
                    examCache.invalidateAll();
                    break;
            }

            // 清空L2缓存（通过模式匹配）
            String pattern = cacheType + ":*";
            redisTemplate.delete(redisTemplate.keys(pattern));
            log.info("已清空{}类型的所有缓存", cacheType);

        } catch (Exception e) {
            log.error("清空缓存失败: {}", cacheType, e);
        }
    }

    /**
     * 预热缓存
     */
    public <T> void warmUp(String cacheType, String key, T data, long timeout, TimeUnit timeUnit) {
        String fullKey = cacheType + ":" + key;
        try {
            // 预热L1缓存
            switch (cacheType) {
                case "stats":
                    statsCache.put(fullKey, data);
                    break;
                case "user":
                    userCache.put(fullKey, data);
                    break;
                case "question":
                    questionCache.put(fullKey, data);
                    break;
                case "exam":
                    examCache.put(fullKey, data);
                    break;
            }

            // 预热L2缓存
            redisTemplate.opsForValue().set(fullKey, data, timeout, timeUnit);
            log.debug("缓存预热完成: {}", fullKey);

        } catch (Exception e) {
            log.error("缓存预热失败: {}", fullKey, e);
        }
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("=== 缓存统计信息 ===\n");
        stats.append("统计缓存: ").append(statsCache.stats()).append("\n");
        stats.append("用户缓存: ").append(userCache.stats()).append("\n");
        stats.append("题目缓存: ").append(questionCache.stats()).append("\n");
        stats.append("考试缓存: ").append(examCache.stats()).append("\n");
        return stats.toString();
    }
}
