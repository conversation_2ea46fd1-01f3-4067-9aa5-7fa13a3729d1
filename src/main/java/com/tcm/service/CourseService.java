package com.tcm.service;

import com.github.pagehelper.PageInfo;
import com.tcm.entity.Course;
import com.tcm.entity.CourseTypeStat;
import java.util.List;

public interface CourseService {
    void add(Course course);

    void deleteById(Integer id);

    void deleteBatch(List<Integer> ids);

    void updateById(Course course);

    Course selectById(Integer id);

    List<Course> selectAll(Course course);

    PageInfo<Course> selectPage(Course course, Integer pageNum, Integer pageSize);

    Course getRecommend(String type);

    List<Course> selectTop8(String type);

    long count();

    long getTotalViews();

    List<Course> findLatest(int limit);

    List<CourseTypeStat> getCourseTypeStats();
}
