package com.tcm.service;

import com.github.pagehelper.PageInfo;
import com.tcm.entity.Signin;
import java.util.List;

public interface SigninService {
    void add(Signin signin);

    Signin selectByUserId(Integer id);

    void deleteById(Integer id);

    void deleteBatch(List<Integer> ids);

    void updateById(Signin signin);

    Signin selectById(Integer id);

    List<Signin> selectAll(Signin signin);

    PageInfo<Signin> selectPage(Signin signin, Integer pageNum, Integer pageSize);
}
