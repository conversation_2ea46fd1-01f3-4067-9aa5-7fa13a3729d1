package com.tcm.service;

import com.github.pagehelper.PageInfo;
import com.tcm.entity.Score;
import java.util.List;

public interface ScoreService {
    void add(Score score);

    void deleteById(Integer id);

    void deleteBatch(List<Integer> ids);

    void updateById(Score score);

    Score selectById(Integer id);

    List<Score> selectAll(Score score);

    PageInfo<Score> selectPage(Score score, Integer pageNum, Integer pageSize);

    Score getRecommend();

    List<Score> selectTop8();
}
