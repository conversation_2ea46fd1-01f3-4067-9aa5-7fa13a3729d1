package com.tcm.service;

import cn.dev33.satoken.stp.StpUtil;
import com.tcm.entity.Account;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 认证兼容性服务
 * 提供新旧认证系统的兼容性支持，确保前端平滑过渡
 */
@Slf4j
@Service
public class AuthCompatibilityService {

    @Autowired
    private SaTokenAuthService saTokenAuthService;

    /**
     * 兼容旧版本的登录接口
     * 内部使用Sa-Token，但保持接口兼容性
     */
    public Account compatibleLogin(Account account) {
        try {
            log.info("兼容性登录接口调用，用户名: {}", account.getUsername());
            
            // 使用Sa-Token进行登录
            Account loginResult = saTokenAuthService.login(account);
            
            // 为了兼容性，确保返回的token字段正确
            if (loginResult != null && loginResult.getToken() != null) {
                log.info("兼容性登录成功，用户ID: {}, 角色: {}", loginResult.getId(), loginResult.getRole());
            }
            
            return loginResult;
        } catch (Exception e) {
            log.error("兼容性登录失败", e);
            throw e;
        }
    }

    /**
     * 兼容旧版本的用户信息获取
     */
    public Account compatibleGetCurrentUser() {
        try {
            if (!StpUtil.isLogin()) {
                log.debug("用户未登录，返回空用户信息");
                return new Account();
            }
            
            Account currentUser = saTokenAuthService.getCurrentUser();
            
            // 确保token字段存在（兼容前端）
            if (currentUser != null && currentUser.getToken() == null) {
                currentUser.setToken(StpUtil.getTokenValue());
            }
            
            return currentUser;
        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            return new Account();
        }
    }

    /**
     * 兼容性登出
     */
    public void compatibleLogout() {
        try {
            saTokenAuthService.logout();
            log.info("兼容性登出成功");
        } catch (Exception e) {
            log.error("兼容性登出失败", e);
        }
    }

    /**
     * 检查token有效性（兼容旧版本）
     */
    public boolean isTokenValid(String token) {
        try {
            if (token == null || token.trim().isEmpty()) {
                return false;
            }
            
            // 检查Sa-Token是否有效
            return StpUtil.isLogin();
        } catch (Exception e) {
            log.debug("Token验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取用户权限信息（兼容性接口）
     */
    public boolean hasPermission(String permission) {
        try {
            return saTokenAuthService.hasPermission(permission);
        } catch (Exception e) {
            log.error("权限检查失败", e);
            return false;
        }
    }

    /**
     * 获取用户角色信息（兼容性接口）
     */
    public boolean hasRole(String role) {
        try {
            return saTokenAuthService.hasRole(role);
        } catch (Exception e) {
            log.error("角色检查失败", e);
            return false;
        }
    }

    /**
     * 刷新token（兼容性接口）
     */
    public String refreshToken() {
        try {
            if (!StpUtil.isLogin()) {
                return null;
            }
            
            // Sa-Token自动续签
            StpUtil.renewTimeout(2592000); // 续签30天
            return StpUtil.getTokenValue();
        } catch (Exception e) {
            log.error("刷新token失败", e);
            return null;
        }
    }

    /**
     * 获取登录状态信息
     */
    public LoginStatusInfo getLoginStatus() {
        try {
            LoginStatusInfo status = new LoginStatusInfo();
            status.setIsLogin(StpUtil.isLogin());
            
            if (StpUtil.isLogin()) {
                status.setLoginId(StpUtil.getLoginId().toString());
                status.setTokenValue(StpUtil.getTokenValue());
                status.setTokenTimeout(StpUtil.getTokenTimeout());
                status.setSessionTimeout(StpUtil.getSessionTimeout());
                
                Account currentUser = saTokenAuthService.getCurrentUser();
                status.setUserId(currentUser.getId());
                status.setUsername(currentUser.getUsername());
                status.setRole(currentUser.getRole());
            }
            
            return status;
        } catch (Exception e) {
            log.error("获取登录状态失败", e);
            return new LoginStatusInfo();
        }
    }

    /**
     * 登录状态信息类
     */
    public static class LoginStatusInfo {
        private Boolean isLogin = false;
        private String loginId;
        private String tokenValue;
        private Long tokenTimeout;
        private Long sessionTimeout;
        private Integer userId;
        private String username;
        private String role;

        // Getters and Setters
        public Boolean getIsLogin() { return isLogin; }
        public void setIsLogin(Boolean isLogin) { this.isLogin = isLogin; }
        
        public String getLoginId() { return loginId; }
        public void setLoginId(String loginId) { this.loginId = loginId; }
        
        public String getTokenValue() { return tokenValue; }
        public void setTokenValue(String tokenValue) { this.tokenValue = tokenValue; }
        
        public Long getTokenTimeout() { return tokenTimeout; }
        public void setTokenTimeout(Long tokenTimeout) { this.tokenTimeout = tokenTimeout; }
        
        public Long getSessionTimeout() { return sessionTimeout; }
        public void setSessionTimeout(Long sessionTimeout) { this.sessionTimeout = sessionTimeout; }
        
        public Integer getUserId() { return userId; }
        public void setUserId(Integer userId) { this.userId = userId; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }
    }
}
