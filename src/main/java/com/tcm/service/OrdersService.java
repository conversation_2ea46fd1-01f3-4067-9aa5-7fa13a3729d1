package com.tcm.service;

import com.github.pagehelper.PageInfo;
import com.tcm.entity.Orders;
import java.util.List;

public interface OrdersService {
    void add(Orders orders);

    void deleteById(Integer id);

    void deleteBatch(List<Integer> ids);

    void updateById(Orders orders);

    Orders selectById(Integer id);

    List<Orders> selectAll(Orders orders);

    PageInfo<Orders> selectPage(Orders orders, Integer pageNum, Integer pageSize);
}
