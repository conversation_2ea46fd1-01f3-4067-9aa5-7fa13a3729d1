package com.tcm.service;

import com.github.pagehelper.PageInfo;
import com.tcm.entity.Account;
import com.tcm.entity.User;
import java.time.LocalDate;
import java.util.List;

public interface UserService {
    void add(User user);

    void deleteById(Integer id);

    void deleteBatch(List<Integer> ids);

    void updateById(User user);

    User selectById(Integer id);

    List<User> selectAll(User user);

    PageInfo<User> selectPage(User user, Integer pageNum, Integer pageSize);

    void recharge(Double account);

    Account login(Account account);

    void register(Account account);

    void updatePassword(Account account);

    User getCurrentUser();

    long count();  // 统计用户总数

    long countNewUsersToday();  // 统计今日新增用户

    long countActiveUsers(LocalDate date);  // 统计指定日期的活跃用户
    
    // 根据用户ID获取用户信息
    User getUserById(Long userId);
    
    // 根据用户名获取用户信息
    User getUserByUsername(String username);
}
