package com.tcm.service;

import com.github.pagehelper.PageInfo;
import com.tcm.entity.Information;
import java.util.List;

public interface InformationService {
    void add(Information information);

    void deleteById(Integer id);

    void deleteBatch(List<Integer> ids);

    void updateById(Information information);

    Information selectById(Integer id);

    List<Information> selectAll(Information information);

    PageInfo<Information> selectPage(Information information, Integer pageNum, Integer pageSize);

    Information getRecommend();

    List<Information> selectTop8();

    long count();  // 统计资料总数

    long countPendingReview();  // 统计待审核资料数量
}
