<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.mapper.FileorderMapper">

    <sql id="Base_Column_List">
        id,file_id,score,order_id,time,user_id
    </sql>

    <select id="selectAll" resultType="com.tcm.entity.Fileorder">
        select fileorder.*, information.img as fileImg, information.name as fileName, user.name as userName
        from fileorder
        left join information on fileorder.file_id = information.id
        left join user on fileorder.user_id = user.id
        <where>
            <if test="id != null"> and id= #{id}</if>
            <if test="userId != null"> and fileorder.user_id = #{userId}</if>
            <if test="orderId != null"> and order_id = #{orderId}</if>
            <if test="fileId != null"> and file_id = #{fileId}</if>
        </where>
        order by id desc
    </select>

    <select id="selectById" resultType="com.tcm.entity.Fileorder">
        select
        <include refid="Base_Column_List" />
        from fileorder
        where id = #{id}
    </select>

    <delete id="deleteById">
        delete from fileorder
        where  id = #{id}
    </delete>

    <insert id="insert" parameterType="com.tcm.entity.Fileorder" useGeneratedKeys="true">
        insert into fileorder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fileId != null">file_id,</if>
            <if test="score != null">score,</if>
            <if test="orderId != null">order_id,</if>
            <if test="time != null">time,</if>
            <if test="userId != null">user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="score != null">#{score},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="time != null">#{time},</if>
            <if test="userId != null">#{userId},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.tcm.entity.Fileorder">
        update fileorder
        <set>
            <if test="fileId != null">
                file_id = #{fileId},
            </if>
            <if test="score != null">
                score = #{score},
            </if>
            <if test="orderId != null">
                order_id = #{orderId},
            </if>
            <if test="time != null">
                time = #{time},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>