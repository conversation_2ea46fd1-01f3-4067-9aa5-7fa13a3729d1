<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.mapper.CourseMapper">

    <sql id="Base_Column_List">
        id,img,name,content,type,price,video,file,discount,recommend
    </sql>

    <select id="selectAll" resultType="com.tcm.entity.Course">
        select
        <include refid="Base_Column_List" />
        from course
        <where>
            <if test="id != null"> and id= #{id}</if>
            <if test="name != null"> and name like concat('%', #{name}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectById" resultType="com.tcm.entity.Course">
        select
        <include refid="Base_Column_List" />
        from course
        where id = #{id}
    </select>

    <delete id="deleteById">
        delete from course
        where  id = #{id}
    </delete>

    <insert id="insert" parameterType="com.tcm.entity.Course" useGeneratedKeys="true">
        insert into course
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="img != null">img,</if>
            <if test="name != null">name,</if>
            <if test="content != null">content,</if>
            <if test="type != null">type,</if>
            <if test="price != null">price,</if>
            <if test="video != null">video,</if>
            <if test="file != null">file,</if>
            <if test="discount != null">discount,</if>
            <if test="recommend != null">recommend,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="img != null">#{img},</if>
            <if test="name != null">#{name},</if>
            <if test="content != null">#{content},</if>
            <if test="type != null">#{type},</if>
            <if test="price != null">#{price},</if>
            <if test="video != null">#{video},</if>
            <if test="file != null">#{file},</if>
            <if test="discount != null">#{discount},</if>
            <if test="recommend != null">#{recommend},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.tcm.entity.Course">
        update course
        <set>
            <if test="img != null">
                img = #{img},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="video != null">
                video = #{video},
            </if>
            <if test="file != null">
                file = #{file},
            </if>
            <if test="discount != null">
                discount = #{discount},
            </if>
            <if test="recommend != null">
                recommend = #{recommend},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>