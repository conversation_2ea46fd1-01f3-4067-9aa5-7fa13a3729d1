<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.mapper.AnswerRecordMapper">

    <sql id="Base_Column_List">
        id,userName,testId,selectedOption,correct,answerDate
    </sql>

    <select id="selectAll" resultType="com.tcm.entity.AnswerRecord">
        select
        <include refid="Base_Column_List" />
        from answerRecord
        <where>
            <if test="id != null"> and id= #{id}</if>
            <if test="userName != null"> and userName like concat('%', #{userName}, '%')</if>
            <if test="testId != null"> and testId= #{testId}</if>
            <if test="selectedOption != null"> and selectedOption= #{selectedOption}</if>
            <if test="correct != null"> and correct= #{correct}</if>
            <if test="answerDate != null"> and answerDate= #{answerDate}</if>
        </where>
    </select>

    <select id="selectById" resultType="com.tcm.entity.AnswerRecord">
        select
        <include refid="Base_Column_List" />
        from answerRecord
        where id = #{id}
    </select>

    <delete id="deleteById">
        delete from answerRecord
        where  id = #{id}
    </delete>

    <insert id="insert" parameterType="com.tcm.entity.AnswerRecord" useGeneratedKeys="true">
        insert into answerRecord
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userName != null">userName,</if>
            <if test="testId != null">testId,</if>
            <if test="selectedOption != null">selectedOption,</if>
            correct,
            <if test="answerDate != null">answerDate,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userName != null">#{userName},</if>
            <if test="testId != null">#{testId},</if>
            <if test="selectedOption != null">#{selectedOption},</if>
            <if test="correct != null">#{correct},</if>
            <if test="answerDate != null">#{answerDate},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.tcm.entity.AnswerRecord">
        update answerRecord
        <set>
            <if test="userName != null">
                userName = #{userName},
            </if>
            <if test="testId != null">
                testId = #{testId},
            </if>
            <if test="selectedOption != null">
                selectedOption = #{selectedOption},
            </if>
            <if test="correct != null">
                correct = #{correct},
            </if>
            <if test="answerDate != null">
                answerDate = #{answerDate},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectTop8IncorrectAnswers" resultType="com.tcm.dto.TestErrorCount">
        SELECT
            t.id AS testId,
            t.name AS testName,
            COUNT(*) AS incorrectCount
        FROM
            answerRecord ar
                JOIN
            test t ON ar.testId = t.id
        WHERE
            ar.correct = 0
        GROUP BY
            ar.testId
        ORDER BY
            incorrectCount DESC
            LIMIT 8
    </select>




</mapper>