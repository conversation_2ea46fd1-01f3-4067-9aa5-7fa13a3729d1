<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.mapper.TestStatusMapper">

    <sql id="Base_Column_List">
        id, user_id, test_id, status, attempt_date, points
    </sql>

    <!-- 查询所有 -->
    <select id="selectAll" resultType="com.tcm.entity.UserTestStatus">
        select
        <include refid="Base_Column_List" />
        from user_test_status
        <where>
            <if test="id != null"> and id = #{id}</if>
            <if test="userId != null"> and user_id = #{userId}</if>
            <if test="testId != null"> and test_id = #{testId}</if>
            <if test="status != null"> and status = #{status}</if>
            <if test="attemptDate != null"> and attempt_date = #{attemptDate}</if>
            <if test="points != null"> and points = #{points}</if>
        </where>
        order by id desc
    </select>

    <!-- 插入操作 -->
    <insert id="insert" parameterType="com.tcm.entity.UserTestStatus" useGeneratedKeys="true" keyProperty="id">
        insert into user_test_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="testId != null">test_id,</if>
            <if test="status != null">status,</if>
            <if test="attemptDate != null">attempt_date,</if>
            <if test="points != null">points,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="testId != null">#{testId},</if>
            <if test="status != null">#{status},</if>
            <if test="attemptDate != null">#{attemptDate},</if>
            <if test="points != null">#{points},</if>
        </trim>
    </insert>

    <!-- 删除操作 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        delete from user_test_status where id = #{id}
    </delete>

    <!-- 更新操作 -->
    <update id="updateById" parameterType="com.tcm.entity.UserTestStatus">
        update user_test_status
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="testId != null">
                test_id = #{testId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="attemptDate != null">
                attempt_date = #{attemptDate},
            </if>
            <if test="points != null">
                points = #{points},
            </if>
        </set>
        where id = #{id}
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" resultType="com.tcm.entity.UserTestStatus">
        select
        <include refid="Base_Column_List" />
        from user_test_status
        where id = #{id}
    </select>

    <select id="findByUserIdAndTestId" resultType="com.tcm.entity.UserTestStatus">
        SELECT * FROM user_test_status
        WHERE user_id = #{userId}
          AND test_id = #{testId}
    </select>

</mapper>
