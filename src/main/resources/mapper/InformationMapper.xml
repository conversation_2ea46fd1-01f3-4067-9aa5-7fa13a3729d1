<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.mapper.InformationMapper">

    <sql id="Base_Column_List">
        id,name,img,score,create_time,recommend,user_id,status,descr,content,file
    </sql>

    <select id="selectAll" resultType="com.tcm.entity.Information">
        select
        <include refid="Base_Column_List" />
        from information
        <where>
            <if test="id != null"> and id= #{id}</if>
            <if test="name != null"> and information.name like concat('%', #{name}, '%')</if>
            <if test="userId != null"> and user_id = #{userId}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectById" resultType="com.tcm.entity.Information">
        select
        <include refid="Base_Column_List" />
        from information
        where id = #{id}
    </select>

    <delete id="deleteById">
        delete from information
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.tcm.entity.Information" useGeneratedKeys="true">
        insert into information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="img != null">img,</if>
            <if test="score != null">score,</if>
            <if test="recommend != null">recommend,</if>
            <if test="userId != null">user_id,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="content != null">#{content},</if>
            <if test="file != null">#{file},</if>
            <if test="type != null">#{type},</if>
            <if test="recommend != null">#{recommend},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.tcm.entity.Information">
        update information
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="img != null">
                img = #{img},
            </if>
            <if test="score != null">
                score = #{score},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="recommend != null">
                recommend = #{recommend},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="descr != null">
                descr = #{descr},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="file != null">
                file = #{file},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>