server:
  port: 9090

# 数据库配置
spring:
  profiles:
    active: dev
  main:
    allow-circular-references: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${tc.datasource.username}
    password: ${tc.datasource.password}
    url: jdbc:mysql://${tc.datasource.host}:${tc.datasource.port}/${tc.datasource.database}?useUnicode=true&characterEncoding=utf-8&allowMultiQueries=true&useSSL=false&serverTimezone=GMT%2b8&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true&cachePrepStmts=true&useServerPrepStmts=true
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      # 连接池配置
      minimum-idle: 5                    # 最小空闲连接数
      maximum-pool-size: 20              # 最大连接池大小
      auto-commit: true                  # 自动提交
      idle-timeout: 30000               # 空闲连接超时时间(毫秒)
      pool-name: TCMHikariCP            # 连接池名称
      max-lifetime: 1800000             # 连接最大生命周期(毫秒)
      connection-timeout: 30000         # 连接超时时间(毫秒)
      connection-test-query: SELECT 1   # 连接测试查询
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
  ai:
    openai:
      options:
        temperature: 0.7
        max-tokens: 2000
        top-p: 0.95
        frequency-penalty: 0
        presence-penalty: 0
  data:
    redis:
      database: ${tc.redis.database}
      host: ${tc.redis.host}
      port: ${tc.redis.port}
      password: ${tc.redis.password}
      timeout: ${tc.redis.timeout}
      lettuce:
        pool:
          max-active: ${tc.redis.lettuce.pool.max-active}
          max-wait: ${tc.redis.lettuce.pool.max-wait}
          max-idle: ${tc.redis.lettuce.pool.max-idle}
          min-idle: ${tc.redis.lettuce.pool.min-idle}

# 配置mybatis实体和xml映射
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.example.entity
  configuration:
    map-underscore-to-camel-case: true
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.tcm.entity
  configuration:
    map-underscore-to-camel-case: true
  type-handlers-package: com.tcm.handler

logging:
  level:
    root: info
    com.tcm: info

# 分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 阿里云oss配置
aliyun:
  oss:
    endpoint: ${tc.aliyun.endpoint}
    accessKeyId: ${tc.aliyun.accessKeyId}
    secret: ${tc.aliyun.secret}
    bucket: ${tc.aliyun.bucket}

# 讯飞星火配置
xf:
  hostUrl: ${tc.xf.hostUrl}
  domain: ${tc.xf.domain}
  temperature: ${tc.xf.temperature}
  maxTokens: ${tc.xf.maxTokens}
  appId: ${tc.xf.appId}
  apiKey: ${tc.xf.apiKey}
  apiSecret: ${tc.xf.apiSecret}
  maxInteractCount: ${tc.xf.maxInteractCount}
  maxUserCount: ${tc.xf.maxUserCount}
  userRecordMaxStatus: ${tc.xf.userRecordMaxStatus}
  maxResponseTime: ${tc.xf.maxResponseTime}
  QPS: ${tc.xf.QPS}
  scheduled:
    updateUserStatusFixedDelay: ${tc.xf.scheduled.updateUserStatusFixedDelay}

# Actuator监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      simple:
        enabled: true

# Sa-Token配置
sa-token:
  # token 名称
  token-name: token
  # token 有效期（单位：秒） 默认30天
  timeout: 2592000
  # 是否允许同一账号多地同时登录
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token
  is-share: true
  # token 风格
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # 是否从 header 中读取 token
  is-read-header: true
  # token前缀
  token-prefix: ""
  # 使用Redis存储token
  alone-redis:
    # 使用Redis存储Sa-Token的数据
    database: ${tc.redis.database}
    host: ${tc.redis.host}
    port: ${tc.redis.port}
    password: ${tc.redis.password}
    timeout: ${tc.redis.timeout}

