tc:
  datasource:
    host: *************
    port: 3306
    database: manager
    username: root
    password: 15556725890.lhq

  aliyun:
    endpoint: oss-cn-shanghai.aliyuncs.com
    accessKeyId: LTAI5tB2Mcsn3r8Gi7R5gb6v
    secret: ******************************
    bucket: tcxkt

  xf:
    hostUrl: https://spark-api.xf-yun.com/v3.5/chat
    domain: generalv3
    temperature: 0.5
    maxTokens: 2048
    appId: 6eb00798
    apiKey: 9af8d0874a5189ada4d5628389a9afa4
    apiSecret: OWY2ZjIwOGM3MWIxYmNkNGI1ODQ1MDU5
    maxInteractCount: 4
    maxUserCount: 20
    userRecordMaxStatus: 6
    maxResponseTime: 40
    QPS: 2
    scheduled:
      updateUserStatusFixedDelay: 600000

  redis:
    database: 1
    host: *************
    port: 6379
    password: 15556725890.lhq
    timeout: 2s
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0

spring:
  ai:
    openai:
      api-key: sk-41f679dce0994eb3a9121240059c9834
      base-url: https://api.deepseek.com/v1
      model: deepseek-chat