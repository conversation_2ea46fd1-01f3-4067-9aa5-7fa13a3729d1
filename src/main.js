import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import '@/assets/css/global.css'
import '@/assets/css/theme/index.css'
import request from "@/utils/request"
import permissionManager from "@/utils/permission"
import PermissionDirectives from "@/directives/permission"
import PermissionButton from "@/components/PermissionButton"
import PermissionContainer from "@/components/PermissionContainer";

Vue.config.productionTip = false

// 注册全局属性
Vue.prototype.$request = request
Vue.prototype.$baseUrl = process.env.VUE_APP_BASEURL
Vue.prototype.$permission = permissionManager

// 注册插件和组件
Vue.use(ElementUI, {size: "small"})
Vue.use(PermissionDirectives)

// 注册全局组件
Vue.component('PermissionButton', PermissionButton)
Vue.component('PermissionContainer', PermissionContainer)

// 创建Vue实例
new Vue({
    router,
    store,
    render: h => h(App),
    async created() {
        // 初始化用户状态
        if (this.$store) {
            await this.$store.dispatch('permission/initUserState')
        }
    }
}).$mount('#app')
