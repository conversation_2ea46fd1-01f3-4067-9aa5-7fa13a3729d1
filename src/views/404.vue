<template>
  <div class="container">
    <div class="content">
      <h1>404</h1>
      <p>哎呀，看起来你迷路了！</p>
      <p>不用担心，使用下面的链接回到安全地带。</p>
      <router-link to="/front/home" class="home-link">带我回家</router-link>
    </div>
  </div>
</template>



<script>
export default {
  name: "404",
  data() {
    return {}
  },
  created() {

  },
  methods: {}
}
</script>

<style scoped>
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  text-align: center;
  background-color: #f4f4f4;
  color: #333;
}

.content {
  max-width: 600px;
  padding: 20px;
}

h1 {
  font-size: 5rem;
  margin: 0.5em 0;
}

p {
  font-size: 1.25rem;
  line-height: 1.6;
  color: #666;
}

.home-link {
  margin-top: 20px;
  display: inline-block;
  background-color: #007bff;
  color: #fff;
  padding: 10px 30px;
  border-radius: 5px;
  text-decoration: none;
  transition: background-color 0.2s ease-in-out;
}

.home-link:hover {
  background-color: #0056b3;
}
</style>


