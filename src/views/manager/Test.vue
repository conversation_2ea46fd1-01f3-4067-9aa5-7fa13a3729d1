<template>
  <div>
    <div class="search">
      <el-input placeholder="请输入科目名称" style="width: 200px" v-model="name"></el-input>
      <el-button type="info" plain style="margin-left: 10px" @click="load(1)">查询</el-button>
      <el-button type="warning" plain style="margin-left: 10px" @click="reset">重置</el-button>
    </div>
    <div class="operation">
      <el-button type="primary" plain @click="handleAdd">新增</el-button>
      <el-button type="danger" plain @click="delBatch">批量删除</el-button>
    </div>
    <div class="table">
      <el-table :data="tableData" stripe  @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="序号" width="80" align="center" sortable></el-table-column>
        <el-table-column prop="name" label="考试名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="content" label="题目内容"></el-table-column>
        <el-table-column prop="options" label="选项内容" show-overflow-tooltip :class="'no-wrap'"></el-table-column>
        <el-table-column prop="answer" label="答案"></el-table-column>
        <el-table-column prop="score" label="分数"></el-table-column>
      </el-table>
    </div>
    <el-dialog :visible.sync="formVisible" title="新增题目">
      <el-form :model="form" ref="formRef">
        <el-form-item label="考试名称" :label-width="formLabelWidth">
          <el-input v-model="form.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="题目内容" :label-width="formLabelWidth " >
          <el-input v-model="form.content" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="选项内容" :label-width="formLabelWidth" >
          <el-input v-model="form.options" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="答案" :label-width="formLabelWidth">
          <el-input v-model="form.answer" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="分数" :label-width="formLabelWidth">
          <el-input v-model="form.score" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="formVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "Test",
  data() {
    return {
      tableData: [],
      selectedRows: [],
      name: '',
      formLabelWidth: '120px',
      formVisible: false,
      form: {
        name: '',
        content: '',
        options: '',
        answer: '',
        score: null,
      },
    };
  },
  created() {
    this.load(1);
  },
  methods: {
    load(pageNum) {
      this.$request.get('/test/selectPage', {
        params: {
          pageNum: pageNum,
          pageSize: 10,
          name: this.name,
        }
      }).then(response => {
        this.tableData = response.data.list;
        this.total = response.data.total;
      }).catch(error => {
        this.$message.error('数据加载失败');
      });
    },
    reset() {
      this.name = '';
      this.load(1);
    },
    handleAdd() {
      this.resetForm();
      this.formVisible = true;
    },
    resetForm() {
      this.form = {
        name: '',
        content: '',
        options: '',
        answer: '',
        score: null,
      };
    },
    submitForm() {
      this.$request.post('/test/add', this.form).then(response => {
        this.$message.success('题目添加成功');
        this.formVisible = false;
        this.load(1);
      }).catch(error => {
        this.$message.error('题目添加失败');
      });
    },
    handleSelectionChange(rows) {
      this.ids = rows.map(v => v.id)
    },
    formatOptions(options) {
      if (!options) return '';
      const optionsObj = typeof options === 'string' ? JSON.parse(options) : options;
      return Object.keys(optionsObj).map(key => `${key}: ${optionsObj[key]}`).join(', ');
    },
    delBatch() {   // 批量删除
      if (!this.ids.length) {
        this.$message.warning('请选择数据')
        return
      }
      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: "warning"}).then(response => {
        this.$request.delete('/test/delete/batch', {data: this.ids}).then(res => {
          if (res.code === '200') {   // 操作成功
            this.$message.success('操作成功')
            this.load(1)
          } else {
            this.$message.error(res.msg)  // 弹出错误的信息
          }
        })
      }).catch(() => {
      })
    },
    del(id) {   // 单个删除
      this.$confirm('您确定删除吗？', '确认删除', {type: "warning"}).then(response => {
        this.$request.delete('/test/delete/' + id).then(res => {
          if (res.code === '200') {   // 表示操作成功
            this.$message.success('操作成功')
            this.load(1)
          } else {
            this.$message.error(res.msg)  // 弹出错误的信息
          }
        })
      }).catch(() => {
      })
    },
  }
}
</script>

<style scoped>

</style>

