<template>
  <div>
    <div class="search">
      <el-input placeholder="请输入课程名称" class="search-input" v-model="name"></el-input>
      <el-button type="info" plain class="margin-left" @click="load(1)">查询</el-button>
      <el-button type="warning" plain class="margin-left" @click="reset">重置</el-button>
    </div>

    <div class="operation">
      <el-button type="primary" plain @click="handleAdd">新增</el-button>
      <el-button type="danger" plain @click="delBatch">批量删除</el-button>
    </div>

    <div class="table">
      <el-table :data="tableData" stripe  @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center"></el-table-column>
        <el-table-column prop="id" label="序号" width="80" align="center" sortable></el-table-column>
        <el-table-column prop="img" label="课程封面" show-overflow-tooltip>
          <template v-slot="scope">
            <div style="display: flex; align-items: center">
              <el-image style="width: 60px; height: 40px; border-radius: 10px" v-if="scope.row.img"
                        :src="scope.row.img" :preview-src-list="[scope.row.img]"></el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="课程名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="content" label="内容" show-overflow-tooltip>
          <template v-slot="scope">
            <el-button type="success" size="mini" @click="viewDataInit(scope.row.content)">点击查看</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="课程类型"></el-table-column>
        <el-table-column prop="price" label="所需积分"></el-table-column>
        <el-table-column prop="video" label="课程视频" show-overflow-tooltip>
          <template v-slot="scope">
            <el-button type="warning" size="mini" @click="down(scope.row.video)" v-if="scope.row.type === 'VIDEO'">点击下载</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="file" label="课程资料" show-overflow-tooltip></el-table-column>
        <el-table-column prop="recommend" label="是否推荐"></el-table-column>

        <el-table-column label="操作" width="180" align="center">
          <template v-slot="scope">
            <el-button plain type="primary" @click="handleEdit(scope.row)" size="mini">编辑</el-button>
            <el-button plain type="danger" size="mini" @click=del(scope.row.id)>删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
            background
            @current-change="handleCurrentChange"
            :current-page="pageNum"
            :page-sizes="[5, 10, 20]"
            :page-size="pageSize"
            layout="total, prev, pager, next"
            :total="total">
        </el-pagination>
      </div>
    </div>


    <el-dialog class="course-dialog" title="课程信息" :visible.sync="fromVisible" :close-on-click-modal="false" destroy-on-close>
      <el-form class="course-form" label-width="100px" :model="form" :rules="rules" ref="formRef">
        <el-form-item label="课程封面">
          <el-upload
            class="avatar-uploader"
            :action="$baseUrl + '/api/files/upload'"
            :headers="{ token: user.token }"
            :on-success="handleImgSuccess"
          >
            <el-button type="primary">上传图片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item prop="name" label="课程名称">
          <el-input v-model="form.name" autocomplete="off" placeholder="请输入课程名称"></el-input>
        </el-form-item>
        <el-form-item prop="type" label="课程类型">
          <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%">
            <el-option label="视频课程" value="VIDEO"></el-option>
            <el-option label="图文课程" value="TEXT"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="recommend" label="是否推荐">
          <el-select v-model="form.recommend" placeholder="请选择" style="width: 100%">
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="price" label="需要积分">
          <el-input v-model="form.price" autocomplete="off" placeholder="请输入所需积分（0表示公开课）"></el-input>
        </el-form-item>
        <el-form-item label="课程视频">
          <el-upload
            class="avatar-uploader"
            :action="$baseUrl + '/api/files/upload'"
            :headers="{ token: user.token }"
            :before-upload="beforeVideoUpload"
            :on-success="handleVideoSuccess"
            :on-error="handleVideoError"
            :file-list="videoFiles">
            <el-button type="primary">上传视频(视频课程需要传)</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item prop="file" label="资料链接">
          <el-input v-model="form.file" autocomplete="off" placeholder="请输入资料链接"></el-input>
        </el-form-item>
        <el-form-item prop="content" label="课程介绍">
          <div id="editor"></div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="fromVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="save"
          :disabled="isUploadingVideo"
        >确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="课程内容" :visible.sync="editorVisible" width="50%" :close-on-click-modal="false" destroy-on-close>
      <div v-html="viewData" class="w-e-text w-e-text-container"></div>
    </el-dialog>

  </div>
</template>

<script>
import E from 'wangeditor'
export default {
  name: "Score",
  data() {
    return {
      editor: null,
      tableData: [],
      pageNum: 1,
      pageSize: 5,
      total: 0,
      name: null,
      recommend: null,
      fromVisible: false,
      editorVisible: false,
      isUploadingVideo: false,
      videoUploadSuccess: false,
      videoChanged: false,
      form: {},
      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
      rules: {
        name: [
          {required: true, message: '请输入课程名称', trigger: 'blur'},
        ],
        type: [
          {required: true, message: '请选择课程类型', trigger: 'blur'},
        ],
        recommend: [
          {required: true, message: '请选择是否推荐', trigger: 'blur'},
        ],
        price: [
          {required: true, message: '请输入课程价格', trigger: 'blur'},
        ],
      },
      ids: [],
      viewData: null
    }
  },
  created() {
    this.load(1)
  },
  methods: {
    initWangEditor(content) {
      this.$nextTick(() => {
        this.editor = new E('#editor')
        this.editor.config.placeholder = '请输入内容'
        this.editor.config.uploadFileName = 'file'
        this.editor.config.uploadImgServer = this.$baseUrl + '/api/files/upload';
        this.editor.create()
        setTimeout(() => {
          this.editor.txt.html(content)
        })
      })
    },
    viewDataInit(data) {
      this.viewData = data
      this.editorVisible = true
    },
    handleAdd() {
      this.form = {}
      this.fromVisible = true
      this.initWangEditor('')
    },
    handleEdit(row) {   // 编辑数据
      this.form = JSON.parse(JSON.stringify(row))
      this.fromVisible = true
      this.initWangEditor(this.form.content || '')
      this.videoUploadSuccess = true;
      this.videoChanged = false;
    },
    save() {
      if (this.isUploadingVideo) {
        this.$message.error('视频仍在上传中，请上传完成后再保存。');
        return;
      }

      this.form.content = this.editor.txt.html();
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.$request({
            url: this.form.id ? '/score/update' : '/score/add',
            method: this.form.id ? 'PUT' : 'POST',
            data: this.form
          }).then(res => {
            if (res.code === '200') {
              this.$message.success('保存成功');
              this.load(1);
              this.fromVisible = false;
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      });
    },
    del(id) {
      this.$confirm('您确定删除吗？', '确认删除', {type: "warning"}).then(response => {
        this.$request.delete('/score/delete/' + id).then(res => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.load(1)
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    handleSelectionChange(rows) {
      this.ids = rows.map(v => v.id)
    },
    delBatch() {   // 批量删除
      if (!this.ids.length) {
        this.$message.warning('请选择数据')
        return
      }
      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: "warning"}).then(response => {
        this.$request.delete('/score/delete/batch', {data: this.ids}).then(res => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.load(1)
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    load(pageNum) {  // 分页查询
      if (pageNum) this.pageNum = pageNum
      this.$request.get('/score/selectPage', {
        params: {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          name: this.name,
          recommend: this.recommend,
        }
      }).then(res => {
        this.tableData = res.data?.list
        this.total = res.data?.total
      })
    },
    reset() {
      this.name = null
      this.recommend = null
      this.load(1)
    },
    handleCurrentChange(pageNum) {
      this.load(pageNum)
    },
    handleImgSuccess(response, file, fileList) {
      if (response && response.url) {
        this.form.img = response.url; // 将OSS返回的图片URL保存到form数据中
        this.$message.success('图片上传成功');
      } else {
        this.$message.error('图片上传失败');
      }
    },
    handleVideoSuccess(response, file, fileList) {
      if (response && response.url) {
        this.form.video = response.url;
        this.$message.success('视频上传成功');
        this.videoUploadSuccess = true;
      } else {
        this.$message.error('视频上传失败');
      }
      this.isUploadingVideo = false;
    },
    handleVideoError() {
      this.$message.error('视频上传失败，请重试');
      this.isUploadingVideo = false;
    },
    beforeVideoUpload() {
      this.isUploadingVideo = true;
      return true;
    },
    down(url) {
      location.href = url
    }
  }
}
</script>

<style scoped>
.search-input,
.select-recommend,
.dialog-size {
  width: 200px;
}

.margin-left {
  margin-left: 10px;
}

.form-padding {
  padding-right: 50px;
}

.dialog-size {
  width: 100%;
}
</style>

