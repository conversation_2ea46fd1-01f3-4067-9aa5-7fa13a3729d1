<template>
  <div>
    <div class="search">
      <el-input class="search-input" placeholder="请输入订单编号" v-model="orderId"></el-input>
      <el-button class="margin-left-10" type="info" plain @click="load(1)">查询</el-button>
      <el-button class="margin-left-10" type="warning" plain @click="reset">重置</el-button>
    </div>

    <div class="table">
      <el-table :data="tableData" stripe>
        <el-table-column prop="id" label="序号" width="80" align="center" sortable></el-table-column>
        <el-table-column prop="courseImg" label="课程封面" width="100">
          <template v-slot="scope">
            <div class="flex-align-center">
              <el-image v-if="scope.row.courseImg" :src="scope.row.courseImg" class="course-img" :preview-src-list="[scope.row.courseImg]"></el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="courseName" label="课程名称" width="150"></el-table-column>
        <el-table-column prop="price" label="订单价格" width="100"></el-table-column>
        <el-table-column prop="orderId" label="订单编号" width="200"></el-table-column>
        <el-table-column prop="time" label="下单时间" width="200"></el-table-column>
        <el-table-column prop="userName" label="下单用户" width="100"></el-table-column>
        <el-table-column label="操作" align="center" width="180">
          <template v-slot="scope">
            <el-button size="mini" type="danger" plain @click="del(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
            background
            @current-change="handleCurrentChange"
            :current-page="pageNum"
            :page-sizes="[5, 10, 20]"
            :page-size="pageSize"
            layout="total, prev, pager, next"
            :total="total">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Orders",
  data() {
    return {
      tableData: [],
      pageNum: 1,
      pageSize: 5,
      total: 0,
      orderId: null,
      fromVisible: false,
      form: {},
      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
      rules: {},
      ids: []
    }
  },
  created() {
    this.load(1)
  },
  methods: {
    load(pageNum) {  // 分页查询
      if (pageNum) this.pageNum = pageNum
      this.$request.get('/orders/selectPage', {
        params: {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          orderId: this.orderId,
        }
      }).then(res => {
        this.tableData = res.data?.list
        this.total = res.data?.total
      })
    },
    reset() {
      this.orderId = null
      this.load(1)
    },
    handleCurrentChange(pageNum) {
      this.load(pageNum)
    },
    del(id) {   // 单个删除
      this.$confirm('您确定删除吗？', '确认删除', {type: "warning"}).then(response => {
        this.$request.delete('/orders/delete/' + id).then(res => {
          if (res.code === '200') {   // 表示操作成功
            this.$message.success('操作成功')
            this.load(1)
          } else {
            this.$message.error(res.msg)  // 弹出错误的信息
          }
        })
      }).catch(() => {
      })
    },
  }
}
</script>

<style scoped>
.search-input {
  width: 200px;
}

.margin-left-10 {
  margin-left: 10px;
}

.flex-align-center {
  display: flex;
  align-items: center;
}

.course-img {
  width: 60px;
  height: 40px;
  border-radius: 10px;
}
</style>

