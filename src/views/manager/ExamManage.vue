<template>
  <div class="exam-manage-container">
    <!-- 搜索区域 -->
    <div class="search-card">
      <el-form :inline="true" :model="searchForm" size="small">
        <el-form-item label="考试名称">
          <el-input v-model="searchForm.name" placeholder="请输入考试名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="考试类型">
          <el-select v-model="searchForm.type" placeholder="请选择考试类型" clearable>
            <el-option 
              v-for="(category, key) in examCategories"
              :key="key"
              :label="category.title" 
              :value="key">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" icon="el-icon-search">查询</el-button>
          <el-button @click="resetSearch" icon="el-icon-refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作区域 -->
    <div class="action-card">
      <el-button type="primary" @click="handleAdd" icon="el-icon-plus">新增考试</el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-card">
      <el-table 
        :data="tableData" 
        border 
        stripe
        v-loading="tableLoading"
        style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
        <el-table-column prop="name" label="考试名称" min-width="200" align="left" show-overflow-tooltip></el-table-column>
        <el-table-column prop="type" label="考试类型" width="120" align="center">
          <template slot-scope="scope">
            <el-tag size="medium">{{ getExamTypeName(scope.row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="题目配置" min-width="300" align="center">
          <template slot-scope="scope">
            <div class="question-tags">
              <el-tag v-if="scope.row.singleCount" size="small" effect="plain" type="primary">
                单选题: {{scope.row.singleCount}}题
              </el-tag>
              <el-tag v-if="scope.row.multipleCount" size="small" effect="plain" type="success">
                多选题: {{scope.row.multipleCount}}题
              </el-tag>
              <el-tag v-if="scope.row.judgeCount" size="small" effect="plain" type="warning">
                判断题: {{scope.row.judgeCount}}题
              </el-tag>
              <el-tag v-if="scope.row.essayCount" size="small" effect="plain" type="info">
                简答题: {{scope.row.essayCount}}题
              </el-tag>
              <el-tag v-if="scope.row.caseCount" size="small" effect="plain" type="danger">
                病例分析: {{scope.row.caseCount}}题
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="考试时长" width="100" align="center">
          <template slot-scope="scope">
            <el-tag size="medium" effect="plain">{{scope.row.duration}}分钟</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="题目总分" width="100" align="center">
          <template slot-scope="scope">
            <span class="total-score">{{ calculateTotalScore(scope.row) }}分</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleEdit(scope.row)">
              <i class="el-icon-edit"></i> 编辑
            </el-button>
            <el-button type="text" size="small" @click="handleQuestions(scope.row)">
              <i class="el-icon-document"></i> 题目管理
            </el-button>
            <el-button type="text" size="small" style="color: #F56C6C" @click="handleDelete(scope.row)">
              <i class="el-icon-delete"></i> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="page"
          :page-size="size"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          background>
        </el-pagination>
      </div>
    </div>

    <!-- 考试信息对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="dialogVisible" 
      width="650px"
      :close-on-click-modal="false"
      :append-to-body="true"
      destroy-on-close
      custom-class="exam-form-dialog">
      <el-form 
        :model="examForm" 
        :rules="rules" 
        ref="examForm" 
        label-width="100px"
        class="exam-form">
        <!-- 基本信息部分 -->
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-form-item label="考试名称" prop="name">
            <el-input v-model="examForm.name" placeholder="请输入考试名称"></el-input>
          </el-form-item>
          <el-form-item label="考试类型" prop="type">
            <el-select v-model="examForm.type" placeholder="请选择考试类型" style="width: 100%">
              <el-option
                v-for="(category, key) in examCategories"
                :key="key"
                :label="category.title"
                :value="key">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="考试时长" prop="duration">
            <el-input-number 
              v-model="examForm.duration" 
              :min="30" 
              :max="180"
              :step="30"
              controls-position="right">
            </el-input-number>
            <span class="unit-label">分钟</span>
          </el-form-item>
        </div>

        <!-- 题目设置部分 -->
        <div class="form-section">
          <div class="section-title">题目设置</div>
          <div class="question-types-grid">
            <el-form-item label="单选题">
              <el-input-number 
                v-model="examForm.singleCount" 
                :min="0" 
                :max="50"
                controls-position="right">
              </el-input-number>
              <span class="score-label">5分/题</span>
            </el-form-item>
            <el-form-item label="多选题">
              <el-input-number 
                v-model="examForm.multipleCount" 
                :min="0" 
                :max="30"
                controls-position="right">
              </el-input-number>
              <span class="score-label">5分/题</span>
            </el-form-item>
            <el-form-item label="判断题">
              <el-input-number 
                v-model="examForm.judgeCount" 
                :min="0" 
                :max="20"
                controls-position="right">
              </el-input-number>
              <span class="score-label">5分/题</span>
            </el-form-item>
            <el-form-item label="简答题">
              <el-input-number 
                v-model="examForm.essayCount" 
                :min="0" 
                :max="10"
                controls-position="right">
              </el-input-number>
              <span class="score-label">15分/题</span>
            </el-form-item>
            <el-form-item label="病例分析">
              <el-input-number 
                v-model="examForm.caseCount" 
                :min="0" 
                :max="5"
                controls-position="right">
              </el-input-number>
              <span class="score-label">20分/题</span>
            </el-form-item>
          </div>
        </div>

        <!-- 分数设置部分 -->
        <div class="form-section">
          <div class="section-title">分数设置</div>
          <div class="score-settings">
            <el-form-item label="总分">
              <div class="total-score">{{ calculateFormTotalScore() }}分</div>
            </el-form-item>
            <el-form-item label="及格分数" prop="passingScore">
              <el-input-number 
                v-model="examForm.passingScore" 
                :min="0" 
                :max="calculateFormTotalScore()"
                :step="5"
                controls-position="right">
              </el-input-number>
              <span class="unit-label">分</span>
            </el-form-item>
          </div>
        </div>
      </el-form>
      <div slot="footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitExam" :loading="submitLoading">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 题目管理抽屉 -->
    <el-drawer
      :title="`${currentExamName || '考试'} - 题目管理`"
      :visible.sync="questionsVisible"
      direction="rtl"
      size="85%"
      :before-close="handleQuestionClose">
      <div class="question-tabs-container">
        <el-tabs v-model="activeQuestionType" type="border-card">
          <el-tab-pane label="单选题" name="single">
            <question-list 
              :examId="currentExamId" 
              type="single"
              @refresh="refreshQuestionCount">
            </question-list>
          </el-tab-pane>
          <el-tab-pane label="多选题" name="multiple">
            <question-list 
              :examId="currentExamId" 
              type="multiple"
              @refresh="refreshQuestionCount">
            </question-list>
          </el-tab-pane>
          <el-tab-pane label="判断题" name="judge">
            <question-list 
              :examId="currentExamId" 
              type="judge"
              @refresh="refreshQuestionCount">
            </question-list>
          </el-tab-pane>
          <el-tab-pane label="简答题" name="essay">
            <question-list 
              :examId="currentExamId" 
              type="essay"
              @refresh="refreshQuestionCount">
            </question-list>
          </el-tab-pane>
          <el-tab-pane label="病例分析" name="case">
            <question-list 
              :examId="currentExamId" 
              type="case"
              @refresh="refreshQuestionCount">
            </question-list>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import QuestionList from '@/components/QuestionList.vue'

export default {
  name: 'ExamManage',
  components: {
    QuestionList
  },
  data() {
    return {
      searchForm: {
        name: '',
        type: ''
      },
      tableData: [],
      page: 1,
      size: 10,
      total: 0,
      dialogVisible: false,
      dialogTitle: '',
      examForm: {
        name: '',
        type: '',
        passingScore: 60,
        singleCount: 0,
        multipleCount: 0,
        judgeCount: 0,
        essayCount: 0,
        caseCount: 0
      },
      rules: {
        name: [
          { required: true, message: '请输入考试名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择考试类型', trigger: 'change' }
        ],
        passingScore: [
          { required: true, message: '请设置及格分数', trigger: 'blur' }
        ]
      },
      questionsVisible: false,
      activeQuestionType: 'single',
      currentExamId: null,
      currentExamName: '',
      examCategories: {
        theory: {
          title: '中医基础理论',
          exams: []
        },
        clinical: {
          title: '诊断治疗',
          exams: []
        },
        classics: {
          title: '经典著作',
          exams: []
        },
        specialist: {
          title: '专科医学',
          exams: []
        }
      },
      formLoading: false,
      submitLoading: false,
    }
  },
  created() {
    this.loadExamList()
  },
  methods: {
    // 获取考试类型名称
    getExamTypeName(type) {
      return this.examCategories[type]?.title || type
    },

    // 计算总分
    calculateTotalScore(exam) {
      return (exam.singleCount + exam.multipleCount + exam.judgeCount) * 5 + 
             exam.essayCount * 15 + exam.caseCount * 20
    },

    // 计算表单总分
    calculateFormTotalScore() {
      const { singleCount = 0, multipleCount = 0, judgeCount = 0, essayCount = 0, caseCount = 0 } = this.examForm
      return (singleCount + multipleCount + judgeCount) * 5 + essayCount * 15 + caseCount * 20
    },

    // 加载考试列表
    async loadExamList() {
      try {
        const res = await this.$request.get('/exam/list', {
          params: {
            page: this.page,
            size: this.size,
            name: this.searchForm.name,
            type: this.searchForm.type
          }
        })
        if (res.code === '200') {
          this.tableData = res.data.records
          this.total = res.data.total
        }
      } catch (error) {
        console.error('获取考试列表失败:', error)
        this.$message.error('获取考试列表失败')
      }
    },

    // 搜索
    handleSearch() {
      this.page = 1
      this.loadExamList()
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        name: '',
        type: ''
      }
      this.handleSearch()
    },

    // 新增考试
    handleAdd() {
      this.dialogTitle = '新增考试'
      this.examForm = {
        name: '',
        type: '',
        passingScore: 60,
        singleCount: 0,
        multipleCount: 0,
        judgeCount: 0,
        essayCount: 0,
        caseCount: 0
      }
      this.dialogVisible = true
    },

    // 编辑考试
    handleEdit(row) {
      this.dialogTitle = '编辑考试'
      this.examForm = {
        id: row.id,
        name: row.name,
        type: row.type,
        duration: row.duration,
        passingScore: row.passingScore,
        singleCount: row.singleCount,
        multipleCount: row.multipleCount,
        judgeCount: row.judgeCount,
        essayCount: row.essayCount,
        caseCount: row.caseCount
      }
      this.dialogVisible = true
    },

    // 删除考试
    handleDelete(row) {
      this.$confirm('确认删除该考试吗？', '提示', {
        type: 'warning'
      }).then(async () => {
        try {
          const res = await this.$request.post(`/exam/delete/${row.id}`)
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.loadExamList()
          }
        } catch (error) {
          this.$message.error('删除失败')
        }
      }).catch(() => {})
    },

    // 处理对话框打开
    handleDialogOpen() {
      this.$nextTick(() => {
        if (this.$refs.examForm) {
          this.$refs.examForm.clearValidate();
        }
      });
    },

    // 处理取消
    handleCancel() {
      this.$refs.examForm.resetFields();
      this.dialogVisible = false;
    },

    // 提交考试表单
    submitExam() {
      this.$refs.examForm.validate(async (valid) => {
        if (valid) {
          try {
            this.submitLoading = true;
            const url = this.examForm.id ? '/exam/update' : '/exam/add';
            const params = {
              ...this.examForm,
              singleCount: String(this.examForm.singleCount),
              multipleCount: String(this.examForm.multipleCount),
              judgeCount: String(this.examForm.judgeCount),
              essayCount: String(this.examForm.essayCount),
              caseCount: String(this.examForm.caseCount)
            };
            const res = await this.$request.post(url, params);
            if (res.code === '200') {
              this.$message.success(this.examForm.id ? '更新成功' : '添加成功');
              this.dialogVisible = false;
              this.loadExamList();
            }
          } catch (error) {
            this.$message.error(this.examForm.id ? '更新失败' : '添加失败');
          } finally {
            this.submitLoading = false;
          }
        }
      });
    },

    // 题目管理
    handleQuestions(row) {
      this.currentExamId = row.id
      this.currentExamName = row.name
      this.questionsVisible = true
      this.activeQuestionType = 'single'
    },

    // 关闭题目管理对话框
    handleQuestionClose(done) {
      this.currentExamId = null
      this.currentExamName = ''
      done()
    },

    // 刷新题目列表
    refreshQuestionCount() {
      // 这里可以添加刷新题目列表的逻辑
    },

    handleSizeChange(val) {
      this.size = val
      this.loadExamList()
    },

    handleCurrentChange(val) {
      this.page = val
      this.loadExamList()
    }
  }
}
</script>

<style lang="scss">
/* 全局样式 */
.exam-manage-container {
  padding: 20px;
  background: #f0f2f5;
  min-height: calc(100vh - 84px);
}

.search-card,
.action-card,
.table-card {
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  padding: 20px;
  margin-bottom: 20px;
}

.search-card {
  .el-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    
    .el-form-item {
      margin-bottom: 0;
      margin-right: 0;
    }
  }
}

.action-card {
  .el-button {
    padding: 9px 15px;
  }
}

.table-card {
  .el-table {
    margin: 15px 0;
    
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: 500;
    }
    
    td {
      padding: 8px 0;
    }
  }
}

.question-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  
  .el-tag {
    margin: 0;
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 对话框样式 */
.exam-form-dialog {
  .el-dialog__body {
    padding: 20px 30px;
  }

  .form-section {
    margin-bottom: 25px;
    
    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 15px;
      padding-left: 10px;
      border-left: 3px solid #409EFF;
    }
  }

  .question-types-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;

    .el-form-item {
      margin-bottom: 0;
    }

    .el-input-number {
      width: 120px;
    }
  }

  .score-settings {
    .total-score {
      font-size: 24px;
      color: #409EFF;
      font-weight: bold;
    }
  }

  .unit-label,
  .score-label {
    margin-left: 10px;
    color: #606266;
    font-size: 14px;
  }

  .score-label {
    color: #909399;
    font-size: 13px;
  }

  .el-form-item {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-input-number {
    width: 160px;
  }
}

.el-drawer__wrapper {
  position: fixed;
  z-index: 3000;
}

.el-drawer__header {
  margin-bottom: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  font-size: 16px;
  font-weight: 500;
}

.question-tabs-container {
  height: calc(100% - 60px);
  padding: 20px;

  .el-tabs {
    height: 100%;
    
    .el-tabs__content {
      height: calc(100% - 40px);
      padding: 20px;
      overflow-y: auto;
    }
  }

  .el-tabs--border-card {
    border: none;
    box-shadow: none;
  }
}

.el-drawer__body {
  height: 100%;
  overflow: hidden;
}
</style> 