<template>
  <div>
    <div class="search">
      <el-input class="search-input" placeholder="请输入课程名称" v-model="name"></el-input>
      <el-button class="search-button" type="info" plain @click="load(1)">查询</el-button>
      <el-button type="warning" plain style="margin-left: 10px" @click="reset">重置</el-button>
    </div>

    <div class="operation">
      <el-button type="primary" plain @click="handleAdd">新增</el-button>
      <el-button type="danger" plain @click="delBatch">批量删除</el-button>
    </div>

    <div class="table">
      <el-table :data="tableData" stripe  @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center"></el-table-column>
        <el-table-column prop="id" label="序号" width="80" align="center" sortable></el-table-column>
        <el-table-column prop="img" label="课程封面" show-overflow-tooltip>
          <template v-slot="scope">
            <div style="display: flex; align-items: center">
              <el-image class="course-cover-image" v-if="scope.row.img" :src="scope.row.img" :preview-src-list="[scope.row.img]"></el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="课程名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="content" label="内容" show-overflow-tooltip>
          <template v-slot="scope">
            <el-button type="success" size="mini" @click="viewDataInit(scope.row.content)">点击查看</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="课程类型"></el-table-column>
        <el-table-column prop="price" label="课程价格"></el-table-column>
        <el-table-column prop="video" label="课程视频" show-overflow-tooltip>
          <template v-slot="scope">
            <el-button type="warning" size="mini" @click="down(scope.row.video)" v-if="scope.row.type === 'VIDEO'">点击下载</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="file" label="课程资料" show-overflow-tooltip></el-table-column>
        <el-table-column prop="discount" label="课程折扣"></el-table-column>
        <el-table-column prop="recommend" label="是否推荐"></el-table-column>

        <el-table-column label="操作" width="180" align="center">
          <template v-slot="scope">
            <el-button plain type="primary" @click="handleEdit(scope.row)" size="mini">编辑</el-button>
            <el-button plain type="danger" size="mini" @click=del(scope.row.id)>删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
            background
            @current-change="handleCurrentChange"
            :current-page="pageNum"
            :page-sizes="[5, 10, 20]"
            :page-size="pageSize"
            layout="total, prev, pager, next"
            :total="total">
        </el-pagination>
      </div>
    </div>


    <el-dialog class="course-dialog" title="课程信息" :visible.sync="fromVisible" :close-on-click-modal="false" destroy-on-close>
      <el-form class="course-form" label-width="100px" :model="form" :rules="rules" ref="formRef">
        <el-form-item label="课程封面">
          <el-upload
            class="avatar-uploader"
            :action="$baseUrl + '/api/files/upload'"
            :headers="{ token: user.token }"
            :on-success="handleImgSuccess"
          >
            <el-button type="primary">上传图片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item prop="name" label="课程名称">
          <el-input v-model="form.name" autocomplete="off" placeholder="请输入课程名称"></el-input>
        </el-form-item>
        <el-form-item prop="type" label="课程类型">
          <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%">
            <el-option label="视频课程" value="VIDEO"></el-option>
            <el-option label="图文课程" value="TEXT"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="recommend" label="是否推荐">
          <el-select v-model="form.recommend" placeholder="请选择" style="width: 100%">
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="price" label="课程价格">
          <el-input v-model="form.price" autocomplete="off" placeholder="请输入价格（0表示公开课）"></el-input>
        </el-form-item>
        <el-form-item label="课程视频">
          <el-upload
            class="avatar-uploader"
            :action="$baseUrl + '/api/files/upload'"
            :headers="{ token: user.token }"
            :before-upload="beforeVideoUpload"
            :on-success="handleVideoSuccess"
            :on-error="handleVideoError"
            :file-list="videoFiles">
            <el-button type="primary">上传视频(视频课程需要传)</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item prop="file" label="资料链接">
          <el-input v-model="form.file" autocomplete="off" placeholder="请输入资料链接"></el-input>
        </el-form-item>
        <el-form-item prop="discount" label="课程折扣">
          <el-input v-model="form.discount" autocomplete="off" placeholder="请输入课程折扣"></el-input>
        </el-form-item>
        <el-form-item prop="content" label="课程介绍">
          <div id="editor"></div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="fromVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="save"
          :disabled="isUploadingVideo"
        >确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="课程内容" :visible.sync="editorVisible" width="50%" :close-on-click-modal="false" destroy-on-close>
      <div v-html="viewData" class="w-e-text w-e-text-container"></div>
    </el-dialog>


  </div>
</template>

<script>
import E from 'wangeditor'
export default {
  name: "Course",
  data() {
    return {
      editor: null,
      viewData: null,
      editorVisible: false,
      tableData: [],  // 所有的数据
      pageNum: 1,   // 当前的页码
      pageSize: 6,  // 每页显示的个数
      total: 0,
      name: null,
      fromVisible: false,
      isUploadingVideo: false,
      videoUploadSuccess: false,
      videoChanged: false,
      form: {},
      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
      rules: {
        name: [
          {required: true, message: '请输入课程名称', trigger: 'blur'},
        ],
        type: [
          {required: true, message: '请选择课程类型', trigger: 'blur'},
        ],
        recommend: [
          {required: true, message: '请选择是否推荐', trigger: 'blur'},
        ],
        price: [
          {required: true, message: '请输入课程价格', trigger: 'blur'},
        ],
      },
      ids: []
    }
  },
  created() {
    this.load(1)
  },
  methods: {
    handleAdd() {   // 新增数据
      this.form = {}  // 新增数据的时候清空数据
      this.fromVisible = true   // 打开弹窗
      this.initWangEditor('')
    },
    handleEdit(row) { // 编辑数据
      this.form = JSON.parse(JSON.stringify(row)) // 深拷贝数据
      this.fromVisible = true // 打开弹窗
      this.initWangEditor(this.form.content || '')
      this.videoUploadSuccess = true; // 假设已有视频是有效的
      this.videoChanged = false; // 开始编辑时，视频未改变
    },
    save() {
      if (this.isUploadingVideo) {
        this.$message.error('视频仍在上传中，请上传完成后再保存。');
        return;
      }

      this.form.content = this.editor.txt.html();
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.$request({
            url: this.form.id ? '/course/update' : '/course/add',
            method: this.form.id ? 'PUT' : 'POST',
            data: this.form
          }).then(res => {
            if (res.code === '200') {
              this.$message.success('保存成功');
              this.load(1);
              this.fromVisible = false;
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      });
    },

    del(id) {   // 单个删除
      this.$confirm('您确定删除吗？', '确认删除', {type: "warning"}).then(response => {
        this.$request.delete('/course/delete/' + id).then(res => {
          if (res.code === '200') {   // 表示操作成功
            this.$message.success('操作成功')
            this.load(1)
          } else {
            this.$message.error(res.msg)  // 弹出错误的信息
          }
        })
      }).catch(() => {
      })
    },
    handleSelectionChange(rows) {   // 当前选中的所有的行数据
      this.ids = rows.map(v => v.id)   //  [1,2]
    },
    delBatch() {   // 批量删除
      if (!this.ids.length) {
        this.$message.warning('请选择数据')
        return
      }
      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: "warning"}).then(response => {
        this.$request.delete('/course/delete/batch', {data: this.ids}).then(res => {
          if (res.code === '200') {   // 表示操作成功
            this.$message.success('操作成功')
            this.load(1)
          } else {
            this.$message.error(res.msg)  // 弹出错误的信息
          }
        })
      }).catch(() => {
      })
    },
    load(pageNum) {  // 分页查询
      if (pageNum) this.pageNum = pageNum
      this.$request.get('/course/selectPage', {
        params: {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          name: this.name,
        }
      }).then(res => {
        this.tableData = res.data?.list
        this.total = res.data?.total
      })
    },
    reset() {
      this.name = null
      this.load(1)
    },
    handleCurrentChange(pageNum) {
      this.load(pageNum)
    },
    handleImgSuccess(response, file, fileList) {
      if (response && response.url) {
        this.form.img = response.url; // 将OSS返回的图片URL保存到form数据中
        this.$message.success('图片上传成功');
      } else {
        this.$message.error('图片上传失败');
      }
    },
    handleVideoSuccess(response, file, fileList) {
      if (response && response.url) {
        this.form.video = response.url;
        this.$message.success('视频上传成功');
        this.videoUploadSuccess = true;
      } else {
        this.$message.error('视频上传失败');
      }
      this.isUploadingVideo = false;
    },
    handleVideoError() {
      this.$message.error('视频上传失败，请重试');
      this.isUploadingVideo = false;
    },
    beforeVideoUpload() {
      this.isUploadingVideo = true;
      return true;
    },
    down(url) {
      location.href = url
    },
    viewDataInit(data) {
      this.viewData = data
      this.editorVisible = true
    },
    initWangEditor(content) {
      this.$nextTick(() => {
        this.editor = new E('#editor')
        this.editor.config.placeholder = '请输入内容'
        this.editor.config.uploadFileName = 'file'
        this.editor.config.uploadImgServer = this.$baseUrl + '/api/files/upload';
        this.editor.create()
        setTimeout(() => {
          this.editor.txt.html(content)
        })
      })
    },
  }
}
</script>

<style scoped>
/* 搜索输入框样式 */
.search-input {
  width: 200px;
}

/* 搜索按钮样式，包含左边距调整 */
.search-button {
  margin-left: 10px;
}

/* 课程封面图片样式 */
.course-cover-image {
  width: 60px;
  height: 40px;
  border-radius: 10px;
}

/* 课程信息对话框样式 */
.course-dialog {
  width: 100%;
}

/* 课程信息表单样式 */
.course-form {
  padding-right: 50px;
}

</style>
