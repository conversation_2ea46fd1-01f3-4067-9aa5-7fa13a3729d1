<template>
  <div class="home-container">
    <!-- 数据概览卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stats-card">
            <div class="icon-wrapper" style="background: rgba(139, 69, 19, 0.1)">
              <i class="el-icon-user" style="color: #8B4513"></i>
            </div>
            <div class="stats-info">
              <div class="stats-title">总用户数</div>
              <div class="stats-number">{{ stats.userCount || 0 }}</div>
              <div class="stats-desc">较昨日 <span style="color: #67C23A">+{{ stats.newUsers || 0 }}</span></div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card">
            <div class="icon-wrapper" style="background: rgba(64, 158, 255, 0.1)">
              <i class="el-icon-reading" style="color: #409EFF"></i>
            </div>
            <div class="stats-info">
              <div class="stats-title">课程总数</div>
              <div class="stats-number">{{ stats.courseCount || 0 }}</div>
              <div class="stats-desc">总访问量 {{ stats.courseViews || 0 }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card">
            <div class="icon-wrapper" style="background: rgba(103, 194, 58, 0.1)">
              <i class="el-icon-document" style="color: #67C23A"></i>
            </div>
            <div class="stats-info">
              <div class="stats-title">资料总数</div>
              <div class="stats-number">{{ stats.resourceCount || 0 }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card">
            <div class="icon-wrapper" style="background: rgba(230, 162, 60, 0.1)">
              <i class="el-icon-medal" style="color: #E6A23C"></i>
            </div>
            <div class="stats-info">
              <div class="stats-title">今日答题人数</div>
              <div class="stats-number">{{ stats.todayExamUsers || 0 }}</div>
              <div class="stats-desc">平均分 {{ stats.avgScore || 0 }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <span class="chart-title">近7天用户活跃度</span>
            </div>
            <div class="chart-content" ref="userChart"></div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <span class="chart-title">课程分类分布</span>
            </div>
            <div class="chart-content" ref="courseChart"></div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 最新动态 -->
    <div class="activity-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="list-card">
            <div class="list-header">
              <span class="list-title">最新课程</span>
              <el-button type="text" @click="$router.push('/course')">查看更多</el-button>
            </div>
            <div class="list-content">
              <div v-for="(course, index) in latestCourses" :key="index" class="list-item">
                <span class="item-title">{{ course.name }}</span>
                <span class="item-time">{{ course.createTime }}</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="list-card">
            <div class="list-header">
              <span class="list-title">考试排行榜</span>
              <el-tag type="danger" size="small">TOP 10</el-tag>
            </div>
            <div class="list-content">
              <div v-for="(user, index) in leaderboard" :key="index" class="list-item">
                <div v-if="index < 3" class="trophy-icon">
                  <i :class="getTrophyIcon(index)" :style="getTrophyStyle(index)"></i>
                </div>
                <div v-else class="rank-number">{{ index + 1 }}</div>
                <div class="user-avatar">
                  <el-avatar :size="32" :src="user.avatar">{{ user.name.substr(0,1) }}</el-avatar>
                </div>
                <span class="item-title">{{ user.name }}</span>
                <span class="score">{{ user.score }}分</span>
              </div>
              <div v-if="leaderboard.length === 0" class="empty-text">
                暂无考试数据
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'Home',
  data() {
    return {
      stats: {
        userCount: 0,
        newUsers: 0,
        courseCount: 0,
        courseViews: 0,
        resourceCount: 0,
        todayExamUsers: 0,
        avgScore: 0
      },
      latestCourses: [],
      leaderboard: [],
      userChartData: [],
      courseTypeData: []
    }
  },
  mounted() {
    this.loadAllData()
  },
  methods: {
    // 加载所有数据
    async loadAllData() {
      await Promise.all([
        this.loadStats(),
        this.loadLatestCourses(),
        this.loadLeaderboard(),
        this.loadUserActivityData(),
        this.loadCourseTypeData()
      ])
      this.initUserChart()
      this.initCourseChart()
    },
    
    // 加载统计数据
    async loadStats() {
      try {
        const res = await this.$request.get('/dashboard/stats')
        if(res.code === '200') {
          this.stats = res.data
        }
      } catch(error) {
        console.error('加载统计数据失败:', error)
        this.$message.error('加载统计数据失败')
      }
    },

    // 加载最新课程
    async loadLatestCourses() {
      try {
        const res = await this.$request.get('/dashboard/latestCourses')
        if(res.code === '200') {
          this.latestCourses = res.data
        }
      } catch(error) {
        console.error('加载最新课程失败:', error)
        this.$message.error('加载最新课程失败')
      }
    },

    // 加载用户活跃度数据
    async loadUserActivityData() {
      try {
        const res = await this.$request.get('/dashboard/userActivity')
        if(res.code === '200') {
          this.userChartData = res.data
        }
      } catch(error) {
        console.error('加载用户活跃度数据失败:', error)
        this.$message.error('加载用户活跃度数据失败')
      }
    },

    // 加载课程分类数据
    async loadCourseTypeData() {
      try {
        const res = await this.$request.get('/dashboard/courseTypes')
        if(res.code === '200') {
          this.courseTypeData = res.data
        }
      } catch(error) {
        console.error('加载课程分类数据失败:', error)
        this.$message.error('加载课程分类数据失败')
      }
    },

    // 添加加载排行榜的方法
    async loadLeaderboard() {
      try {
        const res = await this.$request.get('/dashboard/examLeaderboard')
        if(res.code === '200') {
          this.leaderboard = res.data
        }
      } catch(error) {
        console.error('加载考试排行榜失败:', error)
        this.$message.error('加载考试排行榜失败')
      }
    },

    // 初始化用户活跃度图表
    initUserChart() {
      const chart = echarts.init(this.$refs.userChart)
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: this.userChartData.map(item => item.date)
        },
        yAxis: {
          type: 'value',
          minInterval: 1,
          splitNumber: 5,
          axisLabel: {
            formatter: function(value) {
              return Math.floor(value);
            }
          }
        },
        series: [{
          data: this.userChartData.map(item => Math.floor(item.count)),
          type: 'line',
          smooth: true,
          areaStyle: {
            opacity: 0.3
          },
          itemStyle: {
            color: '#8B4513'
          }
        }]
      }
      chart.setOption(option)
    },

    // 初始化课程分类图表
    initCourseChart() {
      const chart = echarts.init(this.$refs.courseChart)
      const formattedData = this.courseTypeData.map(item => ({
        ...item,
        name: item.name === 'VIDEO' ? '视频课程' : '文本课程'
      }))
      
      // 自定义丰富多彩的颜色
      const colors = [
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#ff9a9e' },
            { offset: 1, color: '#fad0c4' }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#a18cd1' },
            { offset: 1, color: '#fbc2eb' }
          ]
        }
      ];
      
      const option = {
        backgroundColor: '#fff',
        title: {
          text: '课程类型占比分析',
          left: 'center',
          top: 0,
          textStyle: {
            color: '#593113',
            fontSize: 16,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}个 ({d}%)',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderColor: '#e8e0d5',
          borderWidth: 1,
          textStyle: {
            color: '#593113'
          },
          padding: 10,
          extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);'
        },
        legend: {
          orient: 'vertical',
          right: '5%',
          top: 'middle',
          itemWidth: 14,
          itemHeight: 14,
          itemGap: 15,
          icon: 'roundRect',
          textStyle: {
            color: '#593113',
            fontSize: 14
          },
          formatter: name => {
            const item = formattedData.find(i => i.name === name);
            if (!item) return name;
            const percent = (item.value / formattedData.reduce((sum, cur) => sum + cur.value, 0) * 100).toFixed(1);
            return `${name}  ${item.value}个 (${percent}%)`;
          }
        },
        series: [{
          name: '课程分类',
          type: 'pie',
          radius: '65%',
          center: ['40%', '55%'],
          roseType: 'radius',
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2,
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.1)'
          },
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}\n{c}个',
            fontSize: 14,
            color: '#593113',
            distanceToLabelLine: 5
          },
          labelLine: {
            show: true,
            length: 15,
            length2: 10,
            smooth: true
          },
          data: formattedData,
          color: colors,
          animationType: 'scale',
          animationEasing: 'elasticOut',
          animationDelay: function (idx) {
            return Math.random() * 200;
          }
        }]
      }
      chart.setOption(option)
      
      // 添加自适应调整
      window.addEventListener('resize', function() {
        chart.resize();
      });
    },

    // 获取奖杯图标
    getTrophyIcon(index) {
      return 'el-icon-trophy';
    },

    // 获取奖杯样式
    getTrophyStyle(index) {
      const colors = {
        0: '#FFD700',
        1: '#C0C0C0',
        2: '#CD7F32'
      };
      return {
        color: colors[index],
        fontSize: '24px'
      };
    }
  }
}
</script>

<style scoped>
.home-container {
  padding: 20px;
}

.stats-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.icon-wrapper i {
  font-size: 24px;
}

.stats-info {
  flex: 1;
}

.stats-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #2c1810;
  margin-bottom: 4px;
}

.stats-desc {
  font-size: 12px;
  color: #999;
}

.chart-card, .list-card {
  background: #fff;
  border-radius: 8px;
  margin-top: 20px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.chart-header, .list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title, .list-title {
  font-size: 16px;
  font-weight: bold;
  color: #2c1810;
}

.chart-content {
  height: 300px;
}

.list-content .list-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.list-content .list-item:last-child {
  border-bottom: none;
}

.list-content .item-title {
  flex: 1;
  margin: 0 12px;
  color: #2c1810;
}

.list-content .item-time {
  color: #999;
  font-size: 12px;
}

.el-tag {
  margin-right: 8px;
}

.trophy-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.trophy-icon i {
  font-size: 24px;
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.2));
}

.rank-number {
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  background: #f5f5f5;
  border-radius: 12px;
  margin-right: 12px;
  color: #666;
  font-size: 14px;
}

.rank-number.top-three {
  display: none;
}

.user-avatar {
  margin-right: 12px;
}

.score {
  color: #F56C6C;
  font-size: 14px;
  font-weight: bold;
  margin-left: auto;
}

.empty-text {
  text-align: center;
  color: #999;
  padding: 30px 0;
}
</style>
