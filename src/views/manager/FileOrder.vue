<template>
  <div>
    <div class="search">
      <el-input class="search-input" placeholder="请输入订单编号" v-model="orderId"></el-input>
      <el-button class="search-button" type="info" plain @click="load(1)">查询</el-button>
      <el-button class="search-button" type="warning" plain @click="reset">重置</el-button>
    </div>

    <div class="table">
      <el-table :data="tableData" stripe>
        <el-table-column prop="id" label="序号" width="80" align="center" sortable></el-table-column>
        <el-table-column prop="fileImg" label="资料封面" width="100">
          <template v-slot="scope">
            <div style="display: flex; align-items: center">
              <el-image v-if="scope.row.fileImg" :src="scope.row.fileImg" class="file-cover-image" :preview-src-list="[scope.row.fileImg]"></el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="fileName" label="资料名称"></el-table-column>
        <el-table-column prop="score" label="所需积分" width="100"></el-table-column>
        <el-table-column prop="orderId" label="订单编号" width="200"></el-table-column>
        <el-table-column prop="time" label="兑换时间" width="200"></el-table-column>
        <el-table-column prop="userName" label="兑换用户" width="100"></el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination background @current-change="handleCurrentChange" :current-page="pageNum" :page-sizes="[5, 10, 20]" :page-size="pageSize" layout="total, prev, pager, next" :total="total">
        </el-pagination>
      </div>
    </div>
  </div>
</template>


<script>
export default {
  name: "FileOrder",
  data() {
    return {
      tableData: [],  // 所有的数据
      pageNum: 1,   // 当前的页码
      pageSize: 5,  // 每页显示的个数
      total: 0,
      orderId: null,
      fromVisible: false,
      form: {},
      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
      rules: {},
      ids: []
    }
  },
  created() {
    this.load(1)
  },
  methods: {
    load(pageNum) {  // 分页查询
      if (pageNum) this.pageNum = pageNum
      this.$request.get('/fileorder/selectPage', {
        params: {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          orderId: this.orderId,
        }
      }).then(res => {
        this.tableData = res.data?.list
        this.total = res.data?.total
      })
    },
    reset() {
      this.orderId = null
      this.load(1)
    },
    handleCurrentChange(pageNum) {
      this.load(pageNum)
    },
  }
}
</script>

<style scoped>
/* 搜索输入框样式 */
.search-input {
  width: 200px;
}

/* 搜索和重置按钮样式 */
.search-button {
  margin-left: 10px;
}

/* 资料封面图片样式 */
.file-cover-image {
  width: 60px;
  height: 40px;
  border-radius: 10px;
}

</style>
