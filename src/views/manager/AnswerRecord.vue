<template>
  <div>
    <div class="operation">
      <el-button type="danger" plain @click="delBatch">批量删除</el-button>
    </div>
    <div class="table">
      <el-table :data="tableData" stripe @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="序号" width="120" align="center" sortable></el-table-column>
        <el-table-column prop="userName" label="用户名" width="120" align="center"></el-table-column>
        <el-table-column prop="testId" label="测试ID" width="120" align="center"></el-table-column>
        <el-table-column prop="selectedOption" label="选项" width="120" align="center"></el-table-column>
        <el-table-column prop="correct" label="正误信息(正确：1/错误：0)" width="300" align="center"></el-table-column>
        <el-table-column prop="answerDate" label="回答日期" width="120" align="center"></el-table-column>
        <el-table-column label="操作" align="center" width="180">
          <template v-slot="scope">
            <el-button size="mini" type="danger" plain @click="del(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>

  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      selectedRows: [],
    };
  },
  created() {
    this.load();
  },
  methods: {
    load() {
      this.$request.get('/answerRecord/selectPage', {
        params: {
          pageNum: this.currentPage,
          pageSize: this.pageSize
        }
      }).then(response => {
        if (response.code === '200') {
          this.tableData = response.data.list;
          this.total = response.data.total;
        } else {
          this.$message.error('数据加载失败');
        }
      }).catch(error => {
        this.$message.error('数据加载失败');
      });
    },
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.load();
    },
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
      this.load();
    },
    handleSelectionChange(rows) {
      this.ids = rows.map(v => v.id)
    },
    delBatch() {   // 批量删除
      if (!this.ids.length) {
        this.$message.warning('请选择数据')
        return
      }
      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: "warning"}).then(response => {
        this.$request.delete('/answerRecord/delete/batch', {data: this.ids}).then(res => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.load(1)
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    del(id) {   // 单个删除
      this.$confirm('您确定删除吗？', '确认删除', {type: "warning"}).then(response => {
        this.$request.delete('/answerRecord/delete/' + id).then(res => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.load(1)
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
  }
}
</script>

<style scoped>

</style>
