<template>
  <div class="permission-manage">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-icon">
          <i class="el-icon-key"></i>
        </div>
        <div class="header-text">
          <h2>权限管理</h2>
          <p>超级管理员权限分配管理</p>
        </div>
      </div>
    </div>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" class="permission-tabs">
      <!-- 用户权限管理 -->
      <el-tab-pane label="用户权限管理" name="user">
        <div class="tab-content">
          <el-card class="search-card">
            <div slot="header" class="card-header">
              <i class="el-icon-user"></i>
              <span>用户信息查询</span>
            </div>
            <el-form :inline="true" :model="userForm" class="search-form">
              <el-form-item label="用户ID">
                <el-input
                  v-model="userForm.userId"
                  placeholder="请输入用户ID"
                  type="number"
                  class="form-input">
                </el-input>
              </el-form-item>
              <el-form-item label="用户类型">
                <el-select v-model="userForm.userType" placeholder="请选择用户类型" class="form-select">
                  <el-option label="管理员" value="admin"></el-option>
                  <el-option label="学生" value="student"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="loadUserInfo" class="search-btn">
                  <i class="el-icon-search"></i>查询用户
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 用户信息展示 -->
          <el-card v-if="userInfo.loaded" class="user-info-card">
            <div slot="header" class="card-header">
              <i class="el-icon-info"></i>
              <span>用户详细信息</span>
            </div>
            <div class="user-info-content">
              <div class="info-row">
                <span class="info-label">当前角色：</span>
                <div class="info-value">
                  <el-tag
                    v-for="role in userInfo.roles"
                    :key="role.id"
                    :type="getTagType(role.roleCode)"
                    class="role-tag">
                    {{ role.roleName }}
                  </el-tag>
                </div>
              </div>
              <div class="info-row">
                <span class="info-label">权限数量：</span>
                <span class="info-value">
                  <el-tag type="info">{{ userInfo.permissions.length }} 个权限</el-tag>
                </span>
              </div>
            </div>
          </el-card>

          <!-- 角色分配 -->
          <el-card class="role-assign-card">
            <div slot="header" class="card-header">
              <i class="el-icon-s-custom"></i>
              <span>角色分配管理</span>
            </div>
            <el-form class="assign-form">
              <el-form-item label="选择角色">
                <el-select
                  v-model="selectedRoles"
                  multiple
                  placeholder="请选择要分配的角色"
                  class="role-select">
                  <el-option
                    v-for="role in allRoles"
                    :key="role.id"
                    :label="role.roleName"
                    :value="role.id">
                    <span style="float: left">{{ role.roleName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ role.roleCode }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="assignUserRoles" class="action-btn">
                  <i class="el-icon-plus"></i>分配角色
                </el-button>
                <el-button type="danger" @click="revokeUserRoles" class="action-btn">
                  <i class="el-icon-minus"></i>撤销角色
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 角色权限管理 -->
      <el-tab-pane label="角色权限管理" name="role">
        <div class="tab-content">
          <el-card class="role-select-card">
            <div slot="header" class="card-header">
              <i class="el-icon-s-custom"></i>
              <span>角色选择</span>
            </div>
            <el-form class="role-form">
              <el-form-item label="选择角色">
                <el-select
                  v-model="selectedRole"
                  placeholder="请选择要管理的角色"
                  @change="loadRolePermissions"
                  class="role-select">
                  <el-option
                    v-for="role in allRoles"
                    :key="role.id"
                    :label="role.roleName"
                    :value="role.id">
                    <span style="float: left">{{ role.roleName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ role.roleCode }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  @click="updateRolePermissions"
                  :disabled="!selectedRole"
                  class="update-btn">
                  <i class="el-icon-check"></i>更新角色权限
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 权限树 -->
          <el-card v-if="rolePermissionTree.length > 0" class="permission-tree-card">
            <div slot="header" class="card-header">
              <i class="el-icon-menu"></i>
              <span>权限配置</span>
            </div>
            <div class="permission-tree-content">
              <div v-for="category in rolePermissionTree" :key="category.category" class="category-section">
                <div class="category-header">
                  <el-checkbox
                    :indeterminate="getCategoryIndeterminate(category)"
                    :value="getCategoryChecked(category)"
                    @change="handleCategoryChange(category, $event)"
                    class="category-checkbox">
                    <i class="el-icon-folder"></i>
                    {{ category.category }}
                    <el-tag size="mini" type="info">{{ category.permissions.length }}个权限</el-tag>
                  </el-checkbox>
                </div>
                <div class="permission-items">
                  <el-checkbox
                    v-for="permission in category.permissions"
                    :key="permission.id"
                    v-model="permission.assigned"
                    @change="handlePermissionChange"
                    class="permission-checkbox">
                    <div class="permission-info">
                      <span class="permission-name">{{ permission.permissionName }}</span>
                      <span class="permission-code">{{ permission.permissionCode }}</span>
                    </div>
                  </el-checkbox>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 权限树查看 -->
      <el-tab-pane label="系统权限树" name="tree">
        <div class="tab-content">
          <el-card class="tree-control-card">
            <div slot="header" class="card-header">
              <i class="el-icon-refresh"></i>
              <span>权限树控制</span>
            </div>
            <el-button type="primary" @click="loadPermissionTree" class="refresh-btn">
              <i class="el-icon-refresh"></i>刷新权限树
            </el-button>
          </el-card>

          <el-card v-if="systemPermissionTree.length > 0" class="system-tree-card">
            <div slot="header" class="card-header">
              <i class="el-icon-menu"></i>
              <span>系统权限总览</span>
            </div>
            <div class="system-tree-content">
              <div v-for="category in systemPermissionTree" :key="category.category" class="system-category">
                <div class="system-category-header">
                  <h4>
                    <i class="el-icon-folder-opened"></i>
                    {{ category.category }}
                    <el-tag type="primary" size="mini">{{ category.permissions.length }}个权限</el-tag>
                  </h4>
                </div>
                <div class="system-permission-list">
                  <div v-for="permission in category.permissions" :key="permission.id" class="system-permission-item">
                    <div class="permission-main">
                      <i class="el-icon-key"></i>
                      <span class="permission-name">{{ permission.permissionName }}</span>
                      <el-tag size="mini" type="info" class="permission-code-tag">{{ permission.permissionCode }}</el-tag>
                    </div>
                    <div class="permission-description">{{ permission.description }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: "PermissionManage",
  data() {
    return {
      activeTab: 'user',
      userForm: {
        userId: '',
        userType: 'admin'
      },
      userInfo: {
        loaded: false,
        roles: [],
        permissions: []
      },
      allRoles: [],
      selectedRoles: [],
      selectedRole: '',
      rolePermissionTree: [],
      systemPermissionTree: []
    }
  },
  created() {
    this.loadRoles();
    this.loadPermissionTree();
  },
  methods: {
    // 加载所有角色
    async loadRoles() {
      try {
        const res = await this.$request.get('/permission/manage/roles/all');
        if (res.code === '200') {
          this.allRoles = res.data;
        } else {
          this.$message.error(res.msg || '加载角色失败');
        }
      } catch (error) {
        this.$message.error('网络错误');
        console.error('加载角色失败:', error);
      }
    },

    // 加载用户信息
    async loadUserInfo() {
      if (!this.userForm.userId) {
        this.$message.warning('请输入用户ID');
        return;
      }

      try {
        const res = await this.$request.get(`/permission/manage/user/${this.userForm.userId}/${this.userForm.userType}/detail`);
        if (res.code === '200') {
          this.userInfo = {
            loaded: true,
            roles: res.data.roles || [],
            permissions: res.data.permissions || []
          };
          this.$message.success('用户信息加载成功');
        } else {
          this.$message.error(res.msg || '加载用户信息失败');
        }
      } catch (error) {
        this.$message.error('网络错误');
        console.error('加载用户信息失败:', error);
      }
    },

    // 分配用户角色
    async assignUserRoles() {
      if (!this.userForm.userId || this.selectedRoles.length === 0) {
        this.$message.warning('请输入用户ID并选择角色');
        return;
      }

      try {
        const res = await this.$request.post('/permission/manage/user/assign-roles', {
          userId: parseInt(this.userForm.userId),
          userType: this.userForm.userType,
          roleIds: this.selectedRoles,
          operation: 'assign'
        });

        if (res.code === '200') {
          this.$message.success('角色分配成功');
          this.loadUserInfo(); // 重新加载用户信息
        } else {
          this.$message.error(res.msg || '角色分配失败');
        }
      } catch (error) {
        this.$message.error('网络错误');
        console.error('分配角色失败:', error);
      }
    },

    // 撤销用户角色
    async revokeUserRoles() {
      if (!this.userForm.userId || this.selectedRoles.length === 0) {
        this.$message.warning('请输入用户ID并选择要撤销的角色');
        return;
      }

      try {
        const res = await this.$request.post('/permission/manage/user/assign-roles', {
          userId: parseInt(this.userForm.userId),
          userType: this.userForm.userType,
          roleIds: this.selectedRoles,
          operation: 'revoke'
        });

        if (res.code === '200') {
          this.$message.success('角色撤销成功');
          this.loadUserInfo(); // 重新加载用户信息
        } else {
          this.$message.error(res.msg || '角色撤销失败');
        }
      } catch (error) {
        this.$message.error('网络错误');
        console.error('撤销角色失败:', error);
      }
    },

    // 加载角色权限
    async loadRolePermissions() {
      if (!this.selectedRole) return;

      try {
        const res = await this.$request.get(`/permission/manage/role/${this.selectedRole}/tree`);
        if (res.code === '200') {
          this.rolePermissionTree = res.data;
          this.$message.success('角色权限加载成功');
        } else {
          this.$message.error(res.msg || '加载角色权限失败');
        }
      } catch (error) {
        this.$message.error('网络错误');
        console.error('加载角色权限失败:', error);
      }
    },

    // 更新角色权限
    async updateRolePermissions() {
      if (!this.selectedRole) {
        this.$message.warning('请选择角色');
        return;
      }

      // 获取选中的权限
      const selectedPermissions = [];
      this.rolePermissionTree.forEach(category => {
        category.permissions.forEach(permission => {
          if (permission.assigned) {
            selectedPermissions.push(permission.id);
          }
        });
      });

      try {
        const res = await this.$request.post('/permission/manage/role/assign-permissions', {
          roleId: parseInt(this.selectedRole),
          permissionIds: selectedPermissions,
          operation: 'replace'
        });

        if (res.code === '200') {
          this.$message.success('角色权限更新成功');
        } else {
          this.$message.error(res.msg || '角色权限更新失败');
        }
      } catch (error) {
        this.$message.error('网络错误');
        console.error('更新角色权限失败:', error);
      }
    },

    // 加载权限树
    async loadPermissionTree() {
      try {
        const res = await this.$request.get('/permission/manage/tree');
        if (res.code === '200') {
          this.systemPermissionTree = res.data;
        } else {
          this.$message.error(res.msg || '加载权限树失败');
        }
      } catch (error) {
        this.$message.error('网络错误');
        console.error('加载权限树失败:', error);
      }
    },

    // 获取分类的选中状态
    getCategoryChecked(category) {
      const checkedCount = category.permissions.filter(p => p.assigned).length;
      return checkedCount === category.permissions.length;
    },

    // 获取分类的半选状态
    getCategoryIndeterminate(category) {
      const checkedCount = category.permissions.filter(p => p.assigned).length;
      return checkedCount > 0 && checkedCount < category.permissions.length;
    },

    // 处理分类选择变化
    handleCategoryChange(category, checked) {
      category.permissions.forEach(permission => {
        permission.assigned = checked;
      });
    },

    // 处理权限选择变化
    handlePermissionChange() {
      // 这里可以添加权限变化的处理逻辑
    },

    // 获取角色标签类型
    getTagType(roleCode) {
      const tagTypes = {
        'SUPER_ADMIN': 'danger',
        'ADMIN': 'warning',
        'TEACHER': 'success',
        'STUDENT': 'info',
        'USER': 'info'
      }
      return tagTypes[roleCode] || 'info'
    }
  }
}
</script>

<style scoped>
.permission-manage {
  padding: 0;
  background: #f8f5f2;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
}

.header-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.header-icon {
  font-size: 32px;
  margin-right: 15px;
  opacity: 0.9;
}

.header-text h2 {
  margin: 0 0 5px 0;
  font-family: "STKaiti", "楷体", serif;
  font-size: 24px;
}

.header-text p {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

/* 标签页样式 */
.permission-tabs {
  background: transparent;
}

.permission-tabs .el-tabs__header {
  background: #fff;
  border-radius: 8px 8px 0 0;
  margin: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.permission-tabs .el-tabs__item {
  font-family: "STKaiti", "楷体", serif;
  color: #593113;
  font-weight: 500;
}

.permission-tabs .el-tabs__item.is-active {
  color: #8B4513;
}

.permission-tabs .el-tabs__active-bar {
  background-color: #8B4513;
}

.permission-tabs .el-tabs__content {
  padding: 0;
}

/* 标签页内容 */
.tab-content {
  padding: 20px;
}

/* 卡片样式 */
.el-card {
  margin-bottom: 20px;
  border: 1px solid #e8e0d5;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(139, 69, 19, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  font-family: "STKaiti", "楷体", serif;
  color: #593113;
  font-weight: bold;
  font-size: 16px;
}

.card-header i {
  margin-right: 8px;
  color: #8B4513;
  font-size: 18px;
}

/* 表单样式 */
.search-form, .assign-form, .role-form {
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
}

.form-input, .form-select, .role-select {
  width: 200px;
}

.search-btn, .action-btn, .update-btn, .refresh-btn {
  font-family: "STKaiti", "楷体", serif;
  border-radius: 6px;
  padding: 8px 16px;
}

.search-btn {
  background: #8B4513;
  border-color: #8B4513;
}

.search-btn:hover {
  background: #A0522D;
  border-color: #A0522D;
}

/* 用户信息卡片 */
.user-info-content {
  padding: 15px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.info-label {
  font-weight: bold;
  color: #593113;
  width: 100px;
  font-family: "STKaiti", "楷体", serif;
}

.info-value {
  flex: 1;
}

.role-tag {
  margin-right: 8px;
  font-family: "STKaiti", "楷体", serif;
}

/* 权限树样式 */
.permission-tree-content {
  max-height: 600px;
  overflow-y: auto;
}

.category-section {
  border-bottom: 1px solid #e8e0d5;
  margin-bottom: 15px;
}

.category-section:last-child {
  border-bottom: none;
}

.category-header {
  padding: 15px;
  background: linear-gradient(135deg, #f8f5f2 0%, #f0ebe5 100%);
  border-radius: 6px;
  margin-bottom: 10px;
}

.category-checkbox {
  font-weight: bold;
  color: #593113;
  font-family: "STKaiti", "楷体", serif;
}

.permission-items {
  padding-left: 20px;
}

.permission-checkbox {
  display: block;
  margin-bottom: 8px;
  padding: 5px 0;
}

.permission-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.permission-name {
  color: #593113;
  font-weight: 500;
}

.permission-code {
  color: #8B4513;
  font-size: 12px;
  background: rgba(139, 69, 19, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
}

/* 系统权限树样式 */
.system-tree-content {
  max-height: 700px;
  overflow-y: auto;
}

.system-category {
  margin-bottom: 20px;
  border: 1px solid #e8e0d5;
  border-radius: 8px;
  overflow: hidden;
}

.system-category-header {
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  color: white;
  padding: 15px 20px;
}

.system-category-header h4 {
  margin: 0;
  display: flex;
  align-items: center;
  font-family: "STKaiti", "楷体", serif;
}

.system-category-header i {
  margin-right: 10px;
  font-size: 18px;
}

.system-category-header .el-tag {
  margin-left: 10px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
}

.system-permission-list {
  padding: 15px;
  background: #fafafa;
}

.system-permission-item {
  background: white;
  border: 1px solid #e8e0d5;
  border-radius: 6px;
  padding: 12px 15px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.system-permission-item:hover {
  box-shadow: 0 2px 8px rgba(139, 69, 19, 0.1);
  border-color: #8B4513;
}

.permission-main {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.permission-main i {
  color: #8B4513;
  margin-right: 8px;
  font-size: 16px;
}

.permission-name {
  font-weight: 500;
  color: #593113;
  margin-right: 10px;
  font-family: "STKaiti", "楷体", serif;
}

.permission-code-tag {
  background: rgba(139, 69, 19, 0.1);
  color: #8B4513;
  border: none;
}

.permission-description {
  color: #666;
  font-size: 13px;
  margin-left: 24px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-content {
    padding: 10px;
  }

  .form-input, .form-select, .role-select {
    width: 100%;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-label {
    width: auto;
    margin-bottom: 5px;
  }
}
</style>
