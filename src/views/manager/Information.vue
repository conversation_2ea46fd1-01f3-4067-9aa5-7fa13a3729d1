<template>
  <div>
    <div class="search">
      <el-input class="search-input" placeholder="请输入资料名称" v-model="name"></el-input>
      <el-button class="search-button" type="info" plain @click="load(1)">查询</el-button>
      <el-button class="reset-button" type="warning" plain @click="reset">重置</el-button>
    </div>

    <div class="table">
      <el-table :data="tableData" stripe>
        <el-table-column prop="id" label="序号" width="80" align="center" sortable></el-table-column>
        <el-table-column prop="img" label="资料封面" width="100">
          <template v-slot="scope">
            <div style="display: flex; align-items: center">
              <el-image class="material-cover-image" v-if="scope.row.img" :src="scope.row.img" :preview-src-list="[scope.row.img]"></el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="资料名称" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="content" label="资料介绍" width="100">
          <template v-slot="scope">
            <el-button type="success" size="mini" @click="viewDataInit(scope.row.content)">点击查看</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="userName" label="上传用户"></el-table-column>
        <el-table-column prop="time" label="上传时间" width="100"></el-table-column>
        <el-table-column prop="file" label="资料链接" show-overflow-tooltip></el-table-column>
        <el-table-column prop="score" label="所需积分" show-overflow-tooltip></el-table-column>
        <el-table-column prop="recommend" label="是否推荐"></el-table-column>
        <el-table-column prop="status" label="审核状态"></el-table-column>
        <el-table-column prop="descr" label="审核说明"></el-table-column>

        <el-table-column label="操作" width="100" align="center">
          <template v-slot="scope">
            <el-button plain type="primary" @click="handleEdit(scope.row)" size="mini">审核</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
            background
            @current-change="handleCurrentChange"
            :current-page="pageNum"
            :page-sizes="[5, 10, 20]"
            :page-size="pageSize"
            layout="total, prev, pager, next"
            :total="total">
        </el-pagination>
      </div>
    </div>

     <el-dialog class="dialog-form" title="课程信息" :visible.sync="fromVisible" width="55%" :close-on-click-modal="false" destroy-on-close>
      <el-form label-width="100px" :model="form" :rules="rules" ref="formRef">
        <el-form-item prop="file" label="资料链接">
          <a :href="form.file" target="_blank">{{form.file}}</a>
        </el-form-item>
        <el-form-item prop="recommend" label="是否推荐">
          <el-select v-model="form.recommend" placeholder="请选择" style="width: 100%">
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="status" label="审核状态">
          <el-select v-model="form.status" placeholder="请选择" style="width: 100%">
            <el-option label="待审核" value="待审核"></el-option>
            <el-option label="审核通过" value="审核通过"></el-option>
            <el-option label="审核不通过" value="审核不通过"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="descr" label="审核说明">
          <el-input v-model="form.descr" autocomplete="off" placeholder="请输入审核说明"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="fromVisible = false">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="课程内容" :visible.sync="editorVisible" width="50%" :close-on-click-modal="false" destroy-on-close>
      <div v-html="viewData" class="w-e-text w-e-text-container"></div>
    </el-dialog>

  </div>
</template>

<script>
export default {
  name: "Information",
  data() {
    return {
      tableData: [],
      pageNum: 1,
      pageSize: 6,
      total: 0,
      name: null,
      recommend: null,
      fromVisible: false,
      editorVisible: false,
      form: {},
      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
      rules: {
      },
      ids: [],
      viewData: null
    }
  },
  created() {
    this.load(1)
  },
  methods: {
    viewDataInit(data) {
      this.viewData = data
      this.editorVisible = true
    },
    handleEdit(row) {   // 编辑数据
      this.form = JSON.parse(JSON.stringify(row))
      this.fromVisible = true   // 打开弹窗
    },
    save() {   // 保存按钮触发的逻辑  它会触发新增或者更新
      this.$request({
        url: this.form.id ? '/information/update' : '/information/add',
        method: this.form.id ? 'PUT' : 'POST',
        data: this.form
      }).then(res => {
        if (res.code === '200') {  // 表示成功保存
          this.$message.success('保存成功')
          this.load(1)
          this.fromVisible = false
        } else {
          this.$message.error(res.msg)  // 弹出错误的信息
        }
      })
    },
    load(pageNum) {  // 分页查询
      if (pageNum) this.pageNum = pageNum
      this.$request.get('/information/selectPage', {
        params: {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          name: this.name,
          recommend: this.recommend,
        }
      }).then(res => {
        this.tableData = res.data?.list
        this.total = res.data?.total
      })
    },
    reset() {
      this.name = null
      this.recommend = null
      this.load(1)
    },
    handleCurrentChange(pageNum) {
      this.load(pageNum)
    },
  }
}
</script>

<style scoped>
.search-input,
.select-recommend {
  width: 200px;
}

.select-recommend {
  margin-left: 5px;
}

.search-button,
.reset-button {
  margin-left: 10px;
}

.material-cover-image {
  width: 60px;
  height: 40px;
  border-radius: 10px;
}

.dialog-form {
  padding-right: 50px;
}
</style>

