<template>
  <div class="permission-management">
    <div class="search">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="权限类型">
          <el-select v-model="searchForm.category" placeholder="请选择权限类型" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="用户管理" value="用户管理"></el-option>
            <el-option label="课程管理" value="课程管理"></el-option>
            <el-option label="考试管理" value="考试管理"></el-option>
            <el-option label="系统管理" value="系统管理"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadPermissions">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="operation">
      <permission-button 
        permission="system:config"
        type="primary" 
        icon="el-icon-refresh"
        @click="refreshPermissions">
        刷新权限
      </permission-button>
      
      <permission-button 
        admin-only
        type="success" 
        icon="el-icon-view"
        @click="showCurrentUserPermissions">
        查看我的权限
      </permission-button>
    </div>

    <div class="table">
      <el-table :data="filteredPermissions" stripe style="width: 100%">
        <el-table-column prop="code" label="权限代码" width="200"></el-table-column>
        <el-table-column prop="name" label="权限名称" width="150"></el-table-column>
        <el-table-column prop="category" label="权限分类" width="120"></el-table-column>
        <el-table-column label="当前用户是否拥有" width="150">
          <template slot-scope="scope">
            <el-tag :type="hasPermission(scope.row.code) ? 'success' : 'danger'">
              {{ hasPermission(scope.row.code) ? '拥有' : '无权限' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <permission-button 
              permission="system:config"
              type="primary" 
              size="mini"
              @click="testPermission(scope.row.code)">
              测试权限
            </permission-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 角色权限信息 -->
    <div class="role-info" style="margin-top: 20px;">
      <el-card header="角色权限信息">
        <div v-for="role in roles" :key="role.code" class="role-item">
          <h4>{{ role.name }} ({{ role.code }})</h4>
          <p>{{ role.description }}</p>
          <el-tag 
            v-for="permission in getRolePermissions(role.code)" 
            :key="permission" 
            size="mini" 
            style="margin: 2px;">
            {{ permission }}
          </el-tag>
        </div>
      </el-card>
    </div>

    <!-- 权限测试对话框 -->
    <el-dialog title="权限测试" :visible.sync="testDialogVisible" width="500px">
      <div>
        <p><strong>测试权限:</strong> {{ testingPermission }}</p>
        <p><strong>测试结果:</strong> 
          <el-tag :type="testResult ? 'success' : 'danger'">
            {{ testResult ? '有权限' : '无权限' }}
          </el-tag>
        </p>
        <div style="margin-top: 20px;">
          <el-input 
            v-model="customPermission" 
            placeholder="输入自定义权限进行测试"
            @keyup.enter.native="testCustomPermission">
          </el-input>
          <el-button 
            type="primary" 
            style="margin-top: 10px;" 
            @click="testCustomPermission">
            测试自定义权限
          </el-button>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="testDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'PermissionManagement',
  data() {
    return {
      searchForm: {
        category: ''
      },
      permissions: [],
      roles: [],
      currentUserPermissions: [],
      testDialogVisible: false,
      testingPermission: '',
      testResult: false,
      customPermission: ''
    }
  },
  
  computed: {
    ...mapGetters('permission', ['userInfo', 'permissions as userPermissions']),
    
    filteredPermissions() {
      if (!this.searchForm.category) {
        return this.permissions
      }
      return this.permissions.filter(p => p.category === this.searchForm.category)
    }
  },
  
  async mounted() {
    await this.loadPermissions()
    await this.loadRoles()
    await this.loadCurrentUserPermissions()
  },
  
  methods: {
    async loadPermissions() {
      try {
        const res = await this.$request.get('/permission/list')
        if (res.code === '200') {
          this.permissions = res.data
        }
      } catch (error) {
        console.error('加载权限列表失败:', error)
        this.$message.error('加载权限列表失败')
      }
    },
    
    async loadRoles() {
      try {
        const res = await this.$request.get('/permission/roles')
        if (res.code === '200') {
          this.roles = res.data
        }
      } catch (error) {
        console.error('加载角色列表失败:', error)
        this.$message.error('加载角色列表失败')
      }
    },
    
    async loadCurrentUserPermissions() {
      try {
        const permissionData = await this.$permission.getCurrentUserPermissions()
        this.currentUserPermissions = permissionData.permissions
      } catch (error) {
        console.error('加载当前用户权限失败:', error)
      }
    },
    
    hasPermission(permission) {
      return this.currentUserPermissions.includes(permission)
    },
    
    async testPermission(permission) {
      this.testingPermission = permission
      this.testResult = await this.$permission.hasPermission(permission)
      this.testDialogVisible = true
    },
    
    async testCustomPermission() {
      if (!this.customPermission.trim()) {
        this.$message.warning('请输入要测试的权限')
        return
      }
      
      this.testingPermission = this.customPermission
      this.testResult = await this.$permission.hasPermission(this.customPermission)
    },
    
    getRolePermissions(roleCode) {
      // 这里可以调用后端接口获取角色权限
      // 暂时返回示例数据
      if (roleCode === 'ADMIN') {
        return this.permissions.map(p => p.code)
      } else if (roleCode === 'USER') {
        return this.permissions.filter(p => 
          p.category === '课程管理' || p.category === '考试管理'
        ).map(p => p.code)
      }
      return []
    },
    
    async refreshPermissions() {
      this.$permission.clearCache()
      await this.loadPermissions()
      await this.loadCurrentUserPermissions()
      this.$message.success('权限信息已刷新')
    },
    
    async showCurrentUserPermissions() {
      const permissionData = await this.$permission.getCurrentUserPermissions()
      
      this.$alert(`
        <div style="text-align: left;">
          <h4>当前用户权限详情</h4>
          <p><strong>用户:</strong> ${this.userInfo.name} (${this.userInfo.username})</p>
          <p><strong>角色:</strong> ${permissionData.roles.join(', ')}</p>
          <p><strong>权限数量:</strong> ${permissionData.permissions.length}</p>
          <div style="max-height: 300px; overflow-y: auto;">
            <h5>详细权限列表:</h5>
            <ul style="margin: 10px 0; padding-left: 20px;">
              ${permissionData.permissions.map(p => `<li>${p}</li>`).join('')}
            </ul>
          </div>
        </div>
      `, '我的权限信息', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定'
      })
    },
    
    resetSearch() {
      this.searchForm = { category: '' }
    }
  }
}
</script>

<style scoped>
.permission-management {
  padding: 20px;
}

.role-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e8e0d5;
  border-radius: 5px;
  background: #fafafa;
}

.role-item h4 {
  margin: 0 0 10px 0;
  color: #593113;
}

.role-item p {
  margin: 0 0 10px 0;
  color: #666;
}

.search, .operation, .table {
  background: white;
  padding: 20px;
  border-radius: 5px;
  margin-bottom: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
