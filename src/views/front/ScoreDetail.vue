<template>
  <div class="main-content">
    <div class="main-container">
      <div class="center-text">
        <el-button type="success" size="mini">{{ courseData.type === 'VIDEO' ? '视频课' : '图文课' }}</el-button>
        <span class="course-name">{{ courseData.name }}</span>
      </div>
      <div class="center-text course-details">
        <span class="course-price" v-if="courseData.price > 0">{{ courseData.price }} / 积分</span>
        <span class="course-price" v-else>公开课</span>
      </div>
      <div class="course-materials">
        <div class="course-materials-header">课程资料</div>
        <div v-if="courseData.price === 0 || flag" class="course-materials-content">
          <video :src="courseData.video" v-if="courseData.type === 'VIDEO'" controls class="course-video"></video>
          <div class="material-link">资料链接：<a :href="courseData.file" target="_blank">{{ courseData.file }}</a></div>
        </div>
        <div v-else class="button-group">
          <span class="purchase-warning">该课程属于付费课程，购买后可解锁</span>
          <el-button type="warning" size="mini" @click="exchange">购买课程</el-button>
        </div>
      </div>
      <div class="course-introduction">
        <div class="course-introduction-header">课程介绍</div>
        <div v-html="courseData.content" class="w-e-text w-e-text-container"></div>
      </div>
    </div>
  </div>
</template>



<script>
export default {

  data() {
    let scoreId = this.$route.query.id
    return {
      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
      scoreId: scoreId,
      courseData: {},
      flag: false
    }
  },
  mounted() {
    this.loadCourse()
    this.checkOrder()
  },
  // methods：本页面所有的点击事件或者其他函数定义区
  methods: {
    loadCourse() {
      this.$request.get('/score/selectById/' + this.scoreId).then(res => {
        if (res.code === '200') {
          this.courseData = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    checkOrder() {
      this.$request.get('/scoreorder/selectAll', {
        params: {
          userId: this.user.id,
          scoreId: this.scoreId
        }
      }).then(res => {
        if (res.code === '200') {
          if (res.data.length > 0) {
            this.flag = true
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    exchange() {
      let data = {
        scoreId: this.scoreId,
        score: this.courseData.price,
        userId: this.user.id
      }
      this.$request.post('/scoreorder/add', data).then(res => {
        if (res.code === '200') {
          this.$message.success('兑换成功')
          this.loadCourse()
          this.checkOrder()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
  }
}
</script>
<style scoped>
.main-container {
  max-width: 800px;
  margin: 40px auto;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  border-radius: 8px;
  background-color: #fff;
}

.center-text {
  text-align: center;
}

.course-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-top: 10px;
}

.course-details {
  margin-top: 10px;
}

.course-price, .discount {
  font-size: 18px;
  display: inline-block;
  margin-right: 20px;
}

.course-materials-header, .course-introduction-header {
  font-size: 20px;
  font-weight: bold;
  margin: 20px 0 10px;
}

.course-video {
  width: 100%;
  height: auto;
  margin-top: 20px;
}

.material-link {
  font-size: 16px;
  margin-top: 15px;
}

.purchase-warning, .button-group {
  margin-top: 20px;
  text-align: center;
}

.course-introduction {
  margin-top: 30px;
}

.w-e-text-container {
  font-size: 16px;
  line-height: 1.5;
  color: #666;
}

@media (max-width: 768px) {
  .main-container {
    width: 90%;
    margin: 20px auto;
  }
}
</style>

