<template>
  <div class="main-content">
    <div class="main-container">
      <div class="center-text">
        <span class="information-name">{{ informationData.name }}</span>
      </div>
      <div class="center-text information-detail">
        <span class="score-needed" v-if="informationData.score > 0">{{ informationData.score }} / 积分</span>
        <span class="free-access" v-else>免费白嫖</span>
        <span class="publish-time">发布时间：{{ informationData.time }}</span>
      </div>
      <div v-if="informationData.score === 0 || flag" class="material-link-content">
        <div class="material-link">资料链接：<a :href="informationData.file" target="_blank">{{ informationData.file }}</a></div>
      </div>
      <div v-else class="unlock-warning-button-group">
        <span class="unlock-warning">该资料需要积分，兑换后可解锁</span>
        <el-button type="warning" size="mini" @click="exchange">兑换资料</el-button>
      </div>
      <div class="material-introduction">
        <div class="material-introduction-header">资料介绍</div>
        <div v-html="informationData.content" class="w-e-text w-e-text-container"></div>
      </div>
    </div>
  </div>
</template>


<script>
export default {

  data() {
    let informationId = this.$route.query.id
    return {
      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
      informationId: informationId,
      informationData: {},
      flag: false
    }
  },
  mounted() {
    this.loadInformation()
    this.check()
  },
  // methods：本页面所有的点击事件或者其他函数定义区
  methods: {
    loadInformation() {
      this.$request.get('/information/selectById/' + this.informationId).then(res => {
        if (res.code === '200') {
          this.informationData = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    check() {
      this.$request.get('/fileorder/selectAll', {
        params: {
          userId: this.user.id,
          fileId: this.informationId
        }
      }).then(res => {
        if (res.code === '200') {
          if (res.data?.length > 0) {
            this.flag = true
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    exchange() {
      let data = {
        fileId: this.informationId,
        userId: this.user.id,
        score: this.informationData.score
      }
      this.$request.post('/fileorder/add', data).then(res => {
        if (res.code === '200') {
          this.$message.success('兑换成功')
          this.loadInformation()
          this.check()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
  }
}
</script>

<style scoped>
.main-container {
  max-width: 800px;
  margin: 40px auto;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  border-radius: 8px;
  background-color: #fff;
}

.center-text {
  text-align: center;
}

.information-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-top: 10px;
}

.information-detail {
  margin-top: 10px;
}

.score-needed, .free-access {
  font-size: 18px;
  display: inline-block;
  margin-right: 20px;
}

.publish-time {
  font-size: 16px;
  display: block;
  color: #666;
  margin-top: 10px;
}

.material-introduction-header {
  font-size: 20px;
  font-weight: bold;
  margin: 20px 0 10px;
}

.w-e-text-container {
  font-size: 16px;
  line-height: 1.5;
  color: #666;
}

@media (max-width: 768px) {
  .main-container {
    width: 90%;
    margin: 20px auto;
  }
}
</style>


