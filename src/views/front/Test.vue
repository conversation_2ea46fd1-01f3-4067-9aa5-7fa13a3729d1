<template>
  <div class="tcm-exam-container">
    <!-- 顶部banner -->
    <div class="tcm-banner">
      <div class="banner-overlay"></div>
      <div class="banner-content">
        <div class="banner-text">中医学习在线考试</div>
        <div class="banner-desc">传承千年医道 · 精进理论技能</div>
      </div>
    </div>

    <!-- 考试分类区域 -->
    <div class="exam-section">
      <div class="exam-categories">
        <div v-for="(category, key) in examCategories" 
             :key="key" 
             :class="['category-item', key]" 
             @click="showExamList(key)">
          <div class="category-icon">
            <i :class="category.icon"></i>
          </div>
          <div class="category-info">
            <h3>{{category.title}}</h3>
            <p>{{category.desc}}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用抽屉替代对话框 -->
    <el-drawer
      :title="currentCategory?.title"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="50%">
      <div class="exam-drawer-content" v-loading="loading">
        <!-- 考试列表为空时的提示 -->
        <div v-if="examList.length === 0" class="empty-tip">
          <i class="el-icon-warning-outline"></i>
          <p>暂无可用考试</p>
        </div>
        <!-- 考试列表 -->
        <div v-else class="exam-list">
          <div v-for="exam in examList" 
               :key="exam.id" 
               class="exam-item"
               @click="startExam(exam)">
            <div class="exam-title">{{exam.name}}</div>
            <div class="exam-info">
              <el-tag size="small">时长: {{exam.duration}}分钟</el-tag>
              <el-tag size="small" type="success">题量: {{exam.questionCount}}题</el-tag>
              <el-tag size="small" type="warning">及格分: {{exam.passingScore}}分</el-tag>
            </div>
            <div class="exam-desc">
              <i class="el-icon-document"></i>
              {{exam.composition}}
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  data() {
    return {
      examCategories: {
        theory: {
          title: '中医基础理论',
          desc: '夯实中医理论基础',
          icon: 'el-icon-reading'
        },
        clinical: {
          title: '诊断治疗',
          desc: '提升临床诊疗能力',
          icon: 'el-icon-first-aid-kit'
        },
        classics: {
          title: '经典著作',
          desc: '传承经典医学精华',
          icon: 'el-icon-collection'
        },
        specialist: {
          title: '专科医学',
          desc: '专科理论与实践',
          icon: 'el-icon-user'
        }
      },
      currentCategory: null,
      drawerVisible: false,
      examList: [],
      loading: false
    }
  },
  methods: {
    // 显示考试列表
    async showExamList(category) {
      this.currentCategory = this.examCategories[category]
      this.drawerVisible = true
      this.loading = true
      this.examList = [] // 清空之前的列表
      
      try {
        const res = await this.$request.get(`/exam/category/${category}`)
        if (res.code === '200') {
          this.examList = res.data || []
          if (this.examList.length === 0) {
            this.$message.info('该分类下暂无可用考试')
          }
        }
      } catch (error) {
        console.error('获取考试列表失败:', error)
        this.$message.error('获取考试列表失败')
      } finally {
        this.loading = false
      }
    },

    // 开始考试
    async startExam(exam) {
      try {
        // 获取用户信息
        const userStr = localStorage.getItem("xm-user");
        if (!userStr) {
          this.$message.warning('请先登录');
          this.$router.push('/login');
          return;
        }

        const user = JSON.parse(userStr);
        if (!user.id) {
          this.$message.warning('请先登录');
          this.$router.push('/login');
          return;
        }

        // 开始考试
        const res = await this.$request.post('/exam/start', {
          userId: user.id,
          examId: exam.id
        });

        if (res.code === '200') {
          this.drawerVisible = false;
          // 修改跳转路径为 /front/exampage
          this.$router.push({
            path: '/front/exampage',
            query: {
              recordId: res.data.id,
              examId: exam.id
            }
          });
        }
      } catch (error) {
        console.error('开始考试失败:', error);
        this.$message.error('开始考试失败');
      }
    }
  }
}
</script>

<style scoped>
.tcm-exam-container {
  min-height: calc(100vh - 140px);
  background-color: #f8f9fa;
}

.tcm-banner {
  height: 260px;
  position: relative;
  background: url('@/assets/banner/bg.jpeg') center center;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5); /* 半透明遮罩 */
}

.banner-content {
  position: relative;
  z-index: 1;
}

.banner-text {
  font-size: 3.5em;
  font-family: "楷体", KaiTi, serif;
  margin-bottom: 20px;
  letter-spacing: 8px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.banner-desc {
  font-size: 1.4em;
  opacity: 0.9;
  letter-spacing: 4px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.exam-section {
}

.exam-categories {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  max-width: 1200px;
  margin: -50px auto 0; /* 从-80px改为-50px，让卡片向下移动一些 */
  padding: 0 20px 50px;
  position: relative;
  z-index: 2;
}

.category-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.category-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.category-icon {
  font-size: 2.5em;
  margin-bottom: 20px;
}

.category-info h3 {
  margin: 0 0 10px;
  font-size: 1.3em;
  color: #2c3e50;
}

.category-info p {
  margin: 0;
  color: #666;
  font-size: 0.9em;
}

.theory .category-icon { color: #4b6cb7; }
.clinical .category-icon { color: #134e5e; }
.classics .category-icon { color: #c94b4b; }
.specialist .category-icon { color: #373b44; }

.exam-drawer-content {
  padding: 20px;
}

.empty-tip {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.empty-tip i {
  font-size: 48px;
  margin-bottom: 20px;
}

.exam-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.exam-item {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  cursor: pointer;
  transition: all 0.3s;
}

.exam-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px 0 rgba(0,0,0,.15);
}

.exam-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #303133;
}

.exam-info {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.exam-desc {
  color: #606266;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.exam-desc i {
  color: #909399;
}

@media (max-width: 1200px) {
  .exam-categories {
    grid-template-columns: repeat(2, 1fr);
    margin-top: -40px; /* 从-60px改为-40px */
  }
  
  .banner-text {
    font-size: 3em;
  }
}

@media (max-width: 768px) {
  .tcm-banner {
    height: 200px;
  }
  
  .exam-categories {
    grid-template-columns: 1fr;
    margin-top: -30px; /* 从-40px改为-30px */
    padding-bottom: 30px;
  }
  
  .banner-text {
    font-size: 2.2em;
    letter-spacing: 4px;
  }
  
  .banner-desc {
    font-size: 1.1em;
    letter-spacing: 2px;
  }
}
</style>
