<template>
  <div class="tcm-container">
    <!-- 课程标题区域 -->
    <div class="course-header">
      <h1 class="course-title">{{ courseData.name }}</h1>
      <div class="course-meta">
        <span class="meta-tag">
          <i :class="courseData.type === 'VIDEO' ? 'el-icon-video-camera' : 'el-icon-reading'"></i>
          {{ courseData.type === 'VIDEO' ? '视频课程' : '图文课程' }}
        </span>
      </div>
    </div>

    <!-- 课程内容区域 -->
    <div class="course-body">
      <!-- 左侧导航 -->
      <div class="course-nav">
        <div class="nav-item" 
             v-for="(item, index) in navItems" 
             :key="index"
             :class="{ active: currentSection === item.id }"
             @click="scrollToSection(item.id)">
          <div class="nav-icon">
            <i :class="item.icon"></i>
          </div>
          <span class="nav-text">{{ item.name }}</span>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="course-main">
        <!-- 视频/内容区域 -->
        <div id="content" ref="content" class="content-section">
          <div class="section-title">
            <i class="el-icon-video-camera"></i>
            <span>课程内容</span>
          </div>
          <div class="video-container" v-if="courseData.type === 'VIDEO'">
            <video :src="courseData.video" controls class="course-video"></video>
          </div>
          <div class="material-link" v-if="courseData.file">
            <a :href="courseData.file" target="_blank">
              <i class="el-icon-download"></i>
              课程资料
            </a>
          </div>
          <div class="exam-section">
            <div class="exam-header">
              <i class="el-icon-edit-outline"></i>
              <span>课后测试</span>
            </div>
            <div class="exam-content">
              <div class="exam-tip">完成学习后参与测试，检验学习效果</div>
              <el-button class="tcm-btn" @click="openExamDialog">开始测试</el-button>
            </div>
          </div>
        </div>

        <!-- 课程介绍 -->
        <div id="intro" ref="intro" class="content-section">
          <div class="section-title">
            <i class="el-icon-document"></i>
            <span>课程介绍</span>
          </div>
          <div class="rich-content tcm-text" v-html="courseData.content"></div>
        </div>

        <!-- 学习讨论 -->
        <div id="discussion" ref="discussion" class="content-section">
          <div class="section-title">
            <i class="el-icon-chat-dot-round"></i>
            <span>学习讨论</span>
          </div>
          <div class="discussion-area">
            <el-input
              type="textarea"
              :rows="3"
              placeholder="分享你的学习心得..."
              v-model="commentContent"
            ></el-input>
            <el-button class="tcm-btn" @click="submitComment">发表评论</el-button>
            
            <!-- 评论列表 -->
            <div class="comment-list" v-if="comments.length > 0">
              <div class="comment-item" v-for="(comment, index) in comments" :key="index">
                <div class="comment-header">
                  <div class="commenter-info">
                    <img class="commenter-avatar" :src="comment.userAvatar || '/default-avatar.png'" alt="头像">
                    <span class="commenter-name">{{ comment.userName }}</span>
                  </div>
                  <span class="comment-time">{{ comment.createTime }}</span>
                </div>
                <div class="comment-content">
                  {{ comment.content }}
                </div>
                <div class="comment-actions">
                  <span class="action-link" @click="showReplyForm(comment.id)">
                    <i class="el-icon-chat-line-round"></i> 回复
                  </span>
                </div>
                
                <!-- 回复表单 -->
                <div class="reply-form" v-if="activeReplyId === comment.id">
                  <el-input
                    type="textarea"
                    :rows="2"
                    placeholder="回复评论..."
                    v-model="replyContent"
                  ></el-input>
                  <div class="reply-actions">
                    <el-button size="small" @click="cancelReply">取消</el-button>
                    <el-button size="small" type="primary" @click="submitReply(comment.id)">回复</el-button>
                  </div>
                </div>
                
                <!-- 回复列表 -->
                <div class="reply-list" v-if="comment.replies && comment.replies.length > 0">
                  <div class="reply-item" v-for="(reply, replyIndex) in comment.replies" :key="replyIndex">
                    <div class="comment-header">
                      <div class="commenter-info">
                        <img class="commenter-avatar" :src="reply.userAvatar || '/default-avatar.png'" alt="头像">
                        <span class="commenter-name">{{ reply.userName }}</span>
                      </div>
                      <span class="comment-time">{{ reply.createTime }}</span>
                    </div>
                    <div class="comment-content">
                      {{ reply.content }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 没有评论时的提示 -->
            <div class="no-comments" v-else>
              <i class="el-icon-chat-line-square"></i>
              <p>还没有评论，快来发表第一条评论吧！</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加测试对话框 -->
    <el-dialog
      title="课程测试"
      :visible.sync="examDialogVisible"
      width="650px"
      :before-close="handleExamDialogClose"
      :close-on-click-modal="false">
      <div class="exam-dialog-content" v-if="!examFinished">
        <div class="exam-progress">
          <el-progress :percentage="examProgress" :format="progressFormat"></el-progress>
        </div>
        
        <div class="question-container" v-if="currentQuestion">
          <div class="question-title">
            <span class="question-index">{{currentQuestionIndex + 1}}/{{examQuestions.length}}</span>
            {{currentQuestion.content}}
          </div>
          
          <div class="question-options">
            <template v-if="typeof currentQuestion.options === 'object' && currentQuestion.options !== null">
              <el-radio-group v-model="currentAnswer">
                <el-radio v-for="(value, key) in currentQuestion.options" :key="key" :label="key">
                  {{key}}. {{value}}
                </el-radio>
              </el-radio-group>
            </template>
            <template v-else>
              <div class="options-error">
                选项数据加载错误
              </div>
            </template>
          </div>
          
          <div class="question-actions">
            <el-button v-if="currentQuestionIndex > 0" @click="prevQuestion">上一题</el-button>
            <el-button type="primary" @click="nextQuestion" v-if="currentQuestionIndex < examQuestions.length - 1">下一题</el-button>
            <el-button type="success" @click="submitExam" v-else>提交答案</el-button>
          </div>
        </div>
        
        <div v-else class="no-questions">
          <i class="el-icon-loading"></i> 正在加载题目...
        </div>
      </div>
      
      <div class="exam-result" v-else>
        <div class="result-header">
          <i :class="examScore >= 60 ? 'el-icon-success' : 'el-icon-error'"></i>
          <div class="result-title">测试完成</div>
        </div>
        
        <div class="result-score">
          <div class="score-value">{{examScore}}</div>
          <div class="score-label">分数</div>
        </div>
        
        <div class="result-info">
          <p>总题数: <span>{{examQuestions.length}}</span></p>
          <p>正确数: <span>{{examCorrectCount}}</span></p>
          <p>错误数: <span>{{examQuestions.length - examCorrectCount}}</span></p>
        </div>
        
        <div class="result-action">
          <el-button @click="closeExamDialog">关闭</el-button>
          <el-button type="primary" @click="retakeExam">重新测试</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
      courseId: this.$route.query.id,
      courseData: {},
      flag: false,
      commentContent: '',
      comments: [],
      currentSection: 'content',
      navItems: [
        { id: 'content', name: '课程内容', icon: 'el-icon-video-camera' },
        { id: 'intro', name: '课程介绍', icon: 'el-icon-document' },
        { id: 'discussion', name: '学习讨论', icon: 'el-icon-chat-dot-round' }
      ],
      activeReplyId: null,
      replyContent: '',
      examDialogVisible: false,
      examQuestions: [],
      currentQuestionIndex: 0,
      currentAnswer: '',
      userAnswers: [],
      examFinished: false,
      examScore: 0,
      examCorrectCount: 0
    }
  },
  computed: {
    currentQuestion() {
      if (this.examQuestions.length > 0) {
        return this.examQuestions[this.currentQuestionIndex]
      }
      return null
    },
    examProgress() {
      return Math.round((this.currentQuestionIndex + 1) / this.examQuestions.length * 100)
    }
  },
  mounted() {
    this.loadCourse()
    this.checkCourse()
    this.loadComments()
  },
  methods: {
    loadCourse() {
      this.$request.get('/course/selectById/' + this.courseId).then(res => {
        if (res.code === '200') {
          this.courseData = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    checkCourse() {
      this.$request.get('/orders/selectAll', {
        params: {
          userId: this.user.id,
          courseId: this.courseId
        }
      }).then(res => {
        if (res.code === '200') {
          if (res.data.length > 0) {
            this.flag = true
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    buy() {
      let data = {
        courseId: this.courseId,
        userId: this.user.id
      }
      this.$request.post('/orders/add', data).then(res => {
        if (res.code === '200') {
          this.$message.success('购买成功，已解锁课程')
          this.loadCourse()
          this.checkCourse()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    toggleFullScreen() {
      const video = this.$refs.videoPlayer
      if (video) {
        if (video.requestFullscreen) {
          video.requestFullscreen()
        } else if (video.webkitRequestFullscreen) {
          video.webkitRequestFullscreen()
        }
      }
    },
    scrollToSection(sectionId) {
      this.currentSection = sectionId
      const element = this.$refs[sectionId]
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    },
    openExamDialog() {
      if (!this.user.id) {
        this.$message.warning('请先登录再参加测试')
        return
      }
      
      this.examDialogVisible = true
      this.loadExamQuestions()
    },
    loadComments() {
      this.$request.get('/comment/list', {
        params: {
          courseId: this.courseId
        }
      }).then(res => {
        if (res.code === '200') {
          const topComments = []
          const replyMap = {}
          
          res.data.forEach(comment => {
            if (!comment.parentId) {
              comment.replies = []
              topComments.push(comment)
              replyMap[comment.id] = comment
            }
          })
          
          res.data.forEach(comment => {
            if (comment.parentId && replyMap[comment.parentId]) {
              replyMap[comment.parentId].replies.push(comment)
            }
          })
          
          this.comments = topComments
        } else {
          this.$message.error(res.msg || '获取评论失败')
        }
      }).catch(err => {
        console.error('加载评论出错:', err)
        this.$message.error('加载评论失败，请稍后再试')
      })
    },
    submitComment() {
      if (!this.user.id) {
        this.$message.warning('请先登录再评论')
        return
      }
      
      if (!this.commentContent.trim()) {
        this.$message.warning('请输入评论内容')
        return
      }
      
      const data = {
        content: this.commentContent,
        userId: this.user.id,
        courseId: this.courseId,
        createTime: this.formatDate(new Date())
      }
      
      this.$request.post('/comment/add', data).then(res => {
        if (res.code === '200') {
          this.$message.success('评论发表成功')
          this.commentContent = ''
          this.loadComments()
        } else {
          this.$message.error(res.msg || '评论发表失败')
        }
      }).catch(err => {
        console.error('提交评论出错:', err)
        this.$message.error('评论发表失败，请稍后再试')
      })
    },
    showReplyForm(commentId) {
      if (!this.user.id) {
        this.$message.warning('请先登录再回复')
        return
      }
      this.activeReplyId = commentId
      this.replyContent = ''
    },
    cancelReply() {
      this.activeReplyId = null
      this.replyContent = ''
    },
    submitReply(parentId) {
      if (!this.user.id) {
        this.$message.warning('请先登录再回复')
        return
      }
      
      if (!this.replyContent.trim()) {
        this.$message.warning('请输入回复内容')
        return
      }
      
      const data = {
        content: this.replyContent,
        userId: this.user.id,
        courseId: this.courseId,
        parentId: parentId,
        createTime: this.formatDate(new Date())
      }
      
      this.$request.post('/comment/add', data).then(res => {
        if (res.code === '200') {
          this.$message.success('回复发表成功')
          this.replyContent = ''
          this.activeReplyId = null
          this.loadComments()
        } else {
          this.$message.error(res.msg || '回复发表失败')
        }
      }).catch(err => {
        console.error('提交回复出错:', err)
        this.$message.error('回复发表失败，请稍后再试')
      })
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    loadExamQuestions() {
      this.$request.get('/course/test/questions', {
        params: {
          type: 'single',
          count: 5
        }
      }).then(res => {
        if (res.code === '200') {
          this.examQuestions = res.data
          
          // 确保options属性是正确的对象格式
          this.examQuestions.forEach(question => {
            // 检查options是否为字符串，如果是则转换为对象
            if (typeof question.options === 'string') {
              try {
                question.options = JSON.parse(question.options);
              } catch (e) {
                console.error('无法解析选项:', question.options, e);
                question.options = {}; // 解析失败时提供空对象
              }
            } else if (!question.options) {
              question.options = {}; // 确保options存在
            }
            console.log('题目选项处理后:', question.options);
          });
          
          this.userAnswers = new Array(this.examQuestions.length).fill('')
          
          this.currentQuestionIndex = 0
          this.currentAnswer = ''
          this.examFinished = false
        } else {
          this.$message.error(res.msg || '获取题目失败')
        }
      }).catch(err => {
        console.error('加载题目出错:', err)
        this.$message.error('加载题目失败，请稍后再试')
      })
    },
    handleExamDialogClose(done) {
      if (!this.examFinished) {
        this.$confirm('测试尚未完成，确定要退出吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          done()
        }).catch(() => {})
      } else {
        done()
      }
    },
    closeExamDialog() {
      this.examDialogVisible = false
    },
    nextQuestion() {
      if (!this.currentAnswer) {
        this.$message.warning('请先选择一个答案再进入下一题')
        return
      }
      
      this.userAnswers[this.currentQuestionIndex] = this.currentAnswer
      console.log(`已保存第${this.currentQuestionIndex + 1}题答案: ${this.currentAnswer}`)
      
      if (this.currentQuestionIndex < this.examQuestions.length - 1) {
        this.currentQuestionIndex++
        this.currentAnswer = this.userAnswers[this.currentQuestionIndex] || ''
      }
    },
    prevQuestion() {
      if (this.currentAnswer) {
        this.userAnswers[this.currentQuestionIndex] = this.currentAnswer
        console.log(`已保存第${this.currentQuestionIndex + 1}题答案: ${this.currentAnswer}`)
      }
      
      if (this.currentQuestionIndex > 0) {
        this.currentQuestionIndex--
        this.currentAnswer = this.userAnswers[this.currentQuestionIndex] || ''
      }
    },
    submitExam() {
      // 保存最后一道题的答案
      if (this.currentAnswer) {
        this.userAnswers[this.currentQuestionIndex] = this.currentAnswer
      }
      
      // 检查所有题目是否都已作答
      const unansweredIndex = this.userAnswers.findIndex(answer => !answer)
      if (unansweredIndex !== -1) {
        this.$confirm(`第 ${unansweredIndex + 1} 题尚未作答，确定要提交吗？`, '提示', {
          confirmButtonText: '确定提交',
          cancelButtonText: '继续答题',
          type: 'warning'
        }).then(() => {
          this.calculateScore()
        }).catch(() => {
          this.currentQuestionIndex = unansweredIndex
          this.currentAnswer = this.userAnswers[unansweredIndex] || ''
        })
      } else {
        this.calculateScore()
      }
    },
    calculateScore() {
      // 确保所有答案都是字符串格式
      const formattedAnswers = this.userAnswers.map(answer => answer ? answer.toString() : '');
      
      const testData = {
        userId: this.user.id,
        courseId: parseInt(this.courseId),
        answers: formattedAnswers,
        questionIds: this.examQuestions.map(q => q.id)
      }
      
      console.log('提交测试数据:', testData); // 调试用
      
      this.$request.post('/course/test/submit', testData).then(res => {
        if (res.code === '200') {
          this.examScore = res.data.score
          this.examCorrectCount = res.data.correctCount
          this.examFinished = true
        } else {
          this.$message.error(res.msg || '提交答案失败')
        }
      }).catch(err => {
        console.error('提交考试答案出错:', err)
        this.$message.error('提交考试答案失败，请稍后再试')
      })
    },
    retakeExam() {
      this.loadExamQuestions()
    },
    progressFormat(percentage) {
      return `${this.currentQuestionIndex + 1}/${this.examQuestions.length}`
    },
    goToExam() {
      this.openExamDialog()
    }
  }
}
</script>

<style scoped>
.course-header {
  text-align: center;
  padding: 20px 0 30px;
  margin-bottom: 30px;
  border-bottom: 1px solid #e8e0d5;
}

.course-title {
  font-size: 28px;
  color: #2c1810;
  margin: 0 0 20px;
  font-family: "STKaiti", "楷体", serif;
}

.course-meta {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.meta-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 16px;
  background: rgba(139, 69, 19, 0.1);
  color: #8B4513;
  border-radius: 4px;
  font-family: "STKaiti", "楷体", serif;
}

/* 主体布局 */
.course-body {
  display: flex;
  gap: 30px;
}

/* 左侧导航 */
.course-nav {
  width: 200px;
  border: 1px solid #e8e0d5;
  border-radius: 4px;
  padding: 15px 0;
  position: sticky;
  top: 20px;
  height: fit-content;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  transition: all 0.3s;
  margin: 0 10px;
  border-radius: 4px;
  color: #5c3a21;
  font-family: "STKaiti", "楷体", serif;
}

.nav-item:hover {
  background: rgba(139, 69, 19, 0.05);
}

.nav-item.active {
  background: rgba(139, 69, 19, 0.1);
  color: #8B4513;
}

.nav-icon {
  margin-right: 10px;
}

/* 右侧内容区 */
.course-main {
  flex: 1;
}

.content-section {
  border: 1px solid #e8e0d5;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  position: relative;
}

.content-section::after {
  content: '';
  position: absolute;
  top: 6px;
  left: 6px;
  right: 6px;
  bottom: 6px;
  border: 1px solid #e8e0d5;
  pointer-events: none;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  color: #8B4513;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e8e0d5;
  font-family: "STKaiti", "楷体", serif;
}

.video-container {
  margin-bottom: 20px;
}

.course-video {
  width: 100%;
  border-radius: 4px;
}

.material-link a {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #8B4513;
  text-decoration: none;
  padding: 8px 15px;
  border: 1px solid #d4a17d;
  border-radius: 4px;
  font-family: "STKaiti", "楷体", serif;
  transition: all 0.3s;
}

.material-link a:hover {
  background: rgba(139, 69, 19, 0.05);
}

.exam-section {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #e8e0d5;
  border-radius: 4px;
}

.exam-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #8B4513;
  font-family: "STKaiti", "楷体", serif;
  margin-bottom: 15px;
}

.exam-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.exam-tip {
  color: #666;
  font-family: "STKaiti", "楷体", serif;
}

.discussion-area {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.discussion-area .el-button {
  align-self: flex-end;
}

/* 评论列表样式 */
.comment-list {
  margin-top: 20px;
}

.comment-item {
  padding: 15px;
  border: 1px solid #e8e0d5;
  border-radius: 4px;
  margin-bottom: 15px;
  background: rgba(255, 253, 249, 0.5);
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.commenter-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.commenter-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #e8e0d5;
}

.commenter-name {
  font-weight: bold;
  color: #5c3a21;
  font-family: "STKaiti", "楷体", serif;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-content {
  margin-bottom: 10px;
  line-height: 1.6;
  color: #333;
  word-break: break-word;
}

.comment-actions {
  text-align: right;
  margin-top: 10px;
}

.action-link {
  cursor: pointer;
  color: #8B4513;
  font-size: 14px;
  margin-left: 15px;
}

.action-link:hover {
  text-decoration: underline;
}

/* 回复样式 */
.reply-form {
  margin-top: 10px;
  padding: 10px;
  background: rgba(248, 240, 229, 0.3);
  border-radius: 4px;
}

.reply-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 10px;
}

.reply-list {
  margin-top: 15px;
  padding-left: 20px;
}

.reply-item {
  padding: 10px;
  margin-bottom: 10px;
  border-left: 3px solid #d4a17d;
  background: rgba(248, 240, 229, 0.3);
  border-radius: 0 4px 4px 0;
}

/* 无评论样式 */
.no-comments {
  text-align: center;
  padding: 30px 0;
  color: #999;
}

.no-comments i {
  font-size: 48px;
  margin-bottom: 10px;
  color: #d4a17d;
}

/* 测试对话框样式 */
.exam-dialog-content {
  min-height: 400px;
}

.exam-progress {
  margin-bottom: 20px;
}

.question-container {
  padding: 15px;
}

.question-title {
  font-size: 18px;
  margin-bottom: 20px;
  line-height: 1.6;
  color: #333;
  font-family: "STKaiti", "楷体", serif;
}

.question-index {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 4px;
  background: #8B4513;
  color: white;
  font-size: 14px;
  margin-right: 10px;
}

.question-options {
  margin-top: 20px;
  margin-bottom: 30px;
}

.question-options .el-radio {
  display: block;
  margin-left: 0;
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e8e0d5;
  transition: all 0.3s;
}

.question-options .el-radio:hover {
  background: rgba(139, 69, 19, 0.05);
}

.question-options .el-radio.is-checked {
  background: rgba(139, 69, 19, 0.1);
  border-color: #8B4513;
}

.question-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.no-questions {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #999;
  font-size: 16px;
}

.no-questions i {
  margin-right: 10px;
  font-size: 24px;
}

/* 选项错误提示 */
.options-error {
  padding: 15px;
  text-align: center;
  color: #F56C6C;
  background: rgba(245, 108, 108, 0.1);
  border-radius: 4px;
  margin: 15px 0;
}

/* 结果页样式 */
.exam-result {
  text-align: center;
  padding: 20px 0;
}

.result-header {
  margin-bottom: 20px;
}

.result-header i {
  font-size: 60px;
  margin-bottom: 10px;
}

.result-header i.el-icon-success {
  color: #67C23A;
}

.result-header i.el-icon-error {
  color: #F56C6C;
}

.result-title {
  font-size: 24px;
  color: #333;
  font-family: "STKaiti", "楷体", serif;
}

.result-score {
  margin: 30px 0;
}

.score-value {
  font-size: 60px;
  color: #8B4513;
  font-weight: bold;
}

.score-label {
  font-size: 16px;
  color: #999;
  margin-top: 5px;
}

.result-info {
  margin: 20px 0;
  text-align: left;
  display: inline-block;
}

.result-info p {
  margin: 10px 0;
  font-size: 16px;
  color: #666;
}

.result-info span {
  font-weight: bold;
  color: #333;
}

.result-action {
  margin-top: 30px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .course-body {
    flex-direction: column;
  }

  .course-nav {
    width: 100%;
    position: static;
  }

  .exam-content {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
}
</style>


