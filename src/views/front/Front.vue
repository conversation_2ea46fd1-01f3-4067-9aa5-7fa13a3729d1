<template>
  <div class="front-container">
    <!-- 公告栏 -->
    <div class="announcement-bar">
      <i class="el-icon-bell"></i>
      <span class="notice-text">{{ top }}</span>
    </div>
    
    <!-- 头部导航 -->
    <div class="header">
      <div class="header-content">
        <!-- 左侧Logo -->
        <div class="logo-area">
          <img src="@/assets/imgs/logo.png" alt="logo">
          <div class="brand-name">中医传承</div>
        </div>

        <!-- 中间导航菜单 -->
        <div class="nav-area">
          <el-menu :default-active="$route.path" mode="horizontal" router>
            <el-menu-item index="/front/home">
              <i class="el-icon-s-home"></i>首页
            </el-menu-item>
            <el-menu-item index="/front/course">
              <i class="el-icon-reading"></i>课程学习
            </el-menu-item>
            <el-menu-item index="/front/test">
              <i class="el-icon-edit-outline"></i>知识测试
            </el-menu-item>
            <el-menu-item index="/front/score">
              <i class="el-icon-medal"></i>积分兑换
            </el-menu-item>
            <el-menu-item index="/front/information">
              <i class="el-icon-collection"></i>典籍资源
            </el-menu-item>
            <el-menu-item index="/front/myInfo">
              <i class="el-icon-upload"></i>资料分享
            </el-menu-item>
            <el-menu-item index="/front/ai">
              <i class="el-icon-chat-dot-round"></i>智能助手
            </el-menu-item>
          </el-menu>
        </div>

        <!-- 右侧用户区域 -->
        <div class="user-area">
          <template v-if="!user.username">
            <el-button class="auth-btn login" @click="$router.push('/login')">登录</el-button>
            <el-button class="auth-btn register" @click="$router.push('/register')">注册</el-button>
          </template>
          <el-dropdown v-else>
            <div class="user-info">
              <img :src="user.avatar" alt="avatar">
              <span class="username">{{ user.name }}</span>
              <i class="el-icon-arrow-down"></i>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="navTo('/front/person')">
                <i class="el-icon-user"></i>个人中心
              </el-dropdown-item>
              <el-dropdown-item @click.native="navTo('/front/scoreOrder')">
                <i class="el-icon-goods"></i>我的兑换
              </el-dropdown-item>
              <el-dropdown-item @click.native="navTo('/front/FileOrder')">
                <i class="el-icon-document"></i>资源兑换
              </el-dropdown-item>
              <el-dropdown-item @click.native="logout">
                <i class="el-icon-switch-button"></i>退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="main-body">
      <router-view ref="child" @update:user="updateUser" />
    </div>

    <!-- 页脚 -->
    <footer class="footer">
      <p>© 2024 中医传承平台 · 传承国粹 · 弘扬中医</p>
    </footer>
  </div>
</template>

<script>
export default {
  name: "FrontLayout",
  data() {
    return {
      top: '',
      notice: [],
      user: JSON.parse(localStorage.getItem("xm-user") || '{}'),
      noticeInterval: null,
    }
  },
  mounted() {
    this.loadNotice()
  },
  beforeDestroy() {
    if (this.noticeInterval) {
      clearInterval(this.noticeInterval);
    }
  },
  methods: {
    loadNotice() {
      this.$request.get('/notice/selectAll').then(res => {
        this.notice = res.data
        let i = 0
        if (this.notice.length) {
          this.top = this.notice[0].content
          this.noticeInterval = setInterval(() => {
            this.top = this.notice[i].content
            i = (i + 1) % this.notice.length;
          }, 2500)
        }
      })
    },
    updateUser() {
      this.user = JSON.parse(localStorage.getItem('xm-user') || '{}')
    },
    logout() {
      localStorage.removeItem("xm-user");
      this.$router.push("/login");
    },
    navTo(url) {
      this.$router.push(url)
    }
  }
}
</script>

<style>
.front-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f9f6f1;
}

.announcement-bar {
  background: linear-gradient(135deg, #8B4513, #593113);
  padding: 8px 20px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
}

.notice-text {
  margin-left: 8px;
}

.header {
  background: linear-gradient(135deg, #8B4513, #593113);
  box-shadow: 0 2px 12px rgba(89, 49, 19, 0.15);
}

.header-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-area {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-area img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.brand-name {
  color: #fff;
  font-size: 22px;
  font-family: "STKaiti", "楷体", serif;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.nav-area {
  flex: 1;
  display: flex;
  justify-content: center;
}

.user-area {
  display: flex;
  align-items: center;
  gap: 15px;
}

.auth-btn {
  padding: 6px 16px;
  font-size: 14px;
  border-radius: 4px;
  transition: all 0.3s;
}

.login {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.6);
  color: #fff;
}

.login:hover {
  background: rgba(255, 255, 255, 0.1);
}

.register {
  background: #d4a17d;
  border: none;
  color: #593113;
  font-weight: 500;
}

.register:hover {
  background: #e0b48e;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-info img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.username {
  color: #fff;
  font-size: 14px;
}

.main-body {
  flex: 1;
  min-height: calc(100vh - 180px);
}

.footer {
  background: #2c1810;
  color: #d4a17d;
  text-align: center;
  padding: 15px;
  font-family: "STKaiti", "楷体", serif;
}

@media (max-width: 1200px) {
  .header-content {
    padding: 0 15px;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 10px;
  }
  
  .nav-area {
    width: 100%;
    overflow-x: auto;
  }
  
  .user-area {
    margin-top: 10px;
  }
}
</style> 