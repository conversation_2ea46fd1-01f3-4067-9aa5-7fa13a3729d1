<template>
  <div class="main-content">
    <el-card class="form-card">
      <div class="form-header">
        <el-button type="primary" @click="updatePassword">修改密码</el-button>
      </div>
      <el-form :model="user" label-width="100px" class="user-form">
        <div class="avatar-section">
          <el-upload class="avatar-uploader" :action="$baseUrl + '/api/files/upload'" :show-file-list="false" :on-success="handleAvatarSuccess" :headers="{ token: user.token }">
            <img v-if="user.avatar" :src="user.avatar" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户名">
              <el-input v-model="user.username" placeholder="用户名" disabled></el-input>
            </el-form-item>
            <el-form-item label="姓名">
              <el-input v-model="user.name" placeholder="姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话">
              <el-input v-model="user.phone" placeholder="电话"></el-input>
            </el-form-item>
            <el-form-item label="邮箱">
              <el-input v-model="user.email" placeholder="邮箱"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="积分">
              <el-input v-model="user.score" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="form-action">
          <el-button type="primary" @click="update">保存</el-button>
        </div>
      </el-form>
    </el-card>
    <el-dialog title="修改密码" :visible.sync="dialogVisible" width="30%" :close-on-click-modal="false" destroy-on-close>
      <el-form :model="user" label-width="80px" style="padding-right: 20px" :rules="rules" ref="formRef">
        <el-form-item label="原始密码" prop="password">
          <el-input show-password v-model="user.password" placeholder="原始密码"></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input show-password v-model="user.newPassword" placeholder="新密码"></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input show-password v-model="user.confirmPassword" placeholder="确认密码"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="fromVisible = false">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    const validatePassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请确认密码'))
      } else if (value !== this.user.newPassword) {
        callback(new Error('确认密码错误'))
      } else {
        callback()
      }
    }
    return {
      rechargeVisible: false,
      account: null,
      type: '支付宝',
      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
      dialogVisible: false,

      rules: {
        password: [
          { required: true, message: '请输入原始密码', trigger: 'blur' },
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
        ],
        confirmPassword: [
          { validator: validatePassword, required: true, trigger: 'blur' },
        ],
      }
    }
  },
  created() {
      this.loadPerson()
  },
  methods: {
    update() {
      // 保存当前的用户信息到数据库
      this.$request.put('/user/update', this.user).then(res => {
        if (res.code === '200') {
          // 成功更新
          this.$message.success('保存成功')
          // 更新浏览器缓存里的用户信息
          localStorage.setItem('xm-user', JSON.stringify(this.user))

          // 触发父级的数据更新
          this.$emit('update:user')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleAvatarSuccess(response, file, fileList) {
      if (response && response.url) {
        this.user.avatar = response.url; // 将OSS返回的图片URL保存到form数据中
        this.$message.success('图片上传成功');
      } else {
        this.$message.error('图片上传失败');
      }
    },
    // 修改密码
    updatePassword() {
      this.dialogVisible = true
    },
    save() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.$request.put('/updatePassword', this.user).then(res => {
            if (res.code === '200') {
              // 成功更新
              this.$message.success('修改密码成功')
              this.$router.push('/login')
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    initRecharge() {
      this.account = 100
      this.rechargeVisible = true
    },
    recharge() {
      this.$request.get('/user/recharge?account=' + this.account).then(res => {
        if (res.code === '200') {
          this.$message.success('充值成功')
          this.loadPerson()
          this.rechargeVisible = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    loadPerson() {
      this.$request.get('/user/selectById/' + this.user.id).then(res => {
        if (res.code === '200') {
          this.user = res.data
          // 更新浏览器缓存里的用户信息
          localStorage.setItem('xm-user', JSON.stringify(this.user))
        } else {
          this.$message.error(res.msg)
        }
      })
    },
  }
}
</script>

<style scoped>
.main-content {
  display: flex;
  justify-content: center;
  padding: 40px;
}

.form-card {
  width: auto;
  max-width: 1000px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.form-header {
  text-align: right;
  margin-bottom: 50px;
}

.user-form {
  margin-top: 20px;
}

.avatar-section {
  text-align: center;
  margin-bottom: 20px;
}

.avatar-uploader-icon, .avatar {
  width: 90px;
  height: 90px;
  line-height: 100px;
  border-radius: 100%;
}

.avatar-uploader {
  cursor: pointer;
  border: 2px dashed #d9d9d9;
  display: inline-block;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
}

.avatar-uploader:hover {
  border-color: #409EFF;
}

.form-action {
  text-align: center;
  margin-top: 60px;
}

.el-form-item {
}

.el-col {
}
</style>
