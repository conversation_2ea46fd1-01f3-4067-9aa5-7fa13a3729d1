<template>
  <div class="exam-result-container">
    <!-- 结果概览卡片 -->
    <div class="result-overview">
      <div class="score-section">
        <div class="total-score">
          <div class="score-value">{{ examResult.totalScore }}</div>
          <div class="score-label">总分</div>
        </div>
        <div class="score-divider"></div>
        <div class="score-details">
          <div class="detail-item">
            <span class="label">及格分：</span>
            <span class="value">{{ examResult.passingScore }}</span>
          </div>
          <div class="detail-item">
            <span class="label">用时：</span>
            <span class="value">{{ formatDuration(examResult.duration) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">状态：</span>
            <span class="value" :class="{ 'passed': examResult.passed }">
              {{ examResult.passed ? '通过' : '未通过' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 答题情况统计 -->
    <div class="statistics-card">
      <h3>答题情况统计</h3>
      <el-row :gutter="20">
        <el-col :span="8" v-for="(stat, index) in statistics" :key="index">
          <div class="stat-box">
            <div class="stat-title">{{ stat.title }}</div>
            <div class="stat-content">
              <span class="stat-number" :style="{ color: stat.color }">{{ stat.value }}</span>
              <span class="stat-unit">{{ stat.unit }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 题目详情列表 -->
    <div class="questions-detail">
      <h3>详细解析</h3>
      <div v-for="(question, index) in validQuestions" :key="index" class="question-item">
        <div class="question-header">
          <span class="question-type" :class="question.type">
            {{ getQuestionTypeName(question.type) }}
          </span>
          <span class="question-score">
            得分：<span :class="{'score-full': question.score === question.fullScore}">
              {{ question.score }}/{{ question.fullScore }}
            </span>
          </span>
        </div>
        
        <div class="question-content">
          <div class="question-text">{{ index + 1 }}. {{ question.content }}</div>
          
          <!-- 选项展示（单选、多选题） -->
          <div v-if="['single', 'multiple'].includes(question.type)" class="options-list">
            <div v-for="(text, key) in question.options" 
                 :key="key" 
                 class="option-item"
                 :class="{
                   'correct': isCorrectOption(question, key),
                   'wrong': isWrongOption(question, key),
                   'selected': isSelectedOption(question, key)
                 }">
              {{ key }}. {{ text }}
            </div>
          </div>

          <!-- 判断题答案 -->
          <div v-if="question.type === 'judge'" class="judge-answer">
            <div class="option-item"
                 :class="{
                   'correct': question.correctAnswer === question.userAnswer,
                   'wrong': question.correctAnswer !== question.userAnswer
                 }">
              您的答案：{{ question.userAnswer === 'true' ? '正确' : '错误' }}
            </div>
          </div>

          <!-- 简答题答案 -->
          <template v-if="['essay', 'case'].includes(question.type)">
            <div class="answer-section">
              <!-- 病例内容（仅案例分析题显示） -->
              <div v-if="question.type === 'case' && question.caseText" class="case-text">
                <div class="case-title">病例资料：</div>
                <div class="case-content">{{ question.caseText }}</div>
              </div>
              
              <!-- 分开显示每个子问题的答案 -->
              <div v-for="(subQ, subIndex) in question.subQuestions" 
                   :key="subIndex" 
                   class="sub-question-answer">
                <div class="sub-question-title">{{ subQ.question }}</div>
                
                <!-- 考生答案 -->
                <div class="your-answer">
                  <div class="answer-label">您的答案：</div>
                  <div class="answer-content">{{ getUserAnswer(question, subIndex) || '未作答' }}</div>
                </div>
              </div>

              <!-- 参考答案 -->
              <div class="correct-answer">
                <div class="answer-label">参考答案：</div>
                <div class="answer-content">{{ question.correctAnswer }}</div>
              </div>

              <!-- AI评分和解析 -->
              <div class="ai-analysis" v-if="question.analysis">
                <div class="analysis-header">
                  <div class="analysis-title">AI评分解析</div>
                </div>
                <div class="analysis-content">{{ question.analysis }}</div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="action-buttons">
      <el-button @click="$router.push('/front/test')">返回列表</el-button>
      <el-button type="primary" @click="retakeExam">重新考试</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      examResult: {
        totalScore: 0,
        passed: false,
        duration: 0,
        questions: [],
        passingScore: 60, // 默认及格分为60
        examId: null
      },
      loading: false,
      examDetail: null
    }
  },

  computed: {
    statistics() {
      const total = this.validQuestions.length;
      console.log('计算统计信息，当前及格分:', this.examResult.passingScore);
      
      return [
        {
          title: '答题总数',
          value: total,
          unit: '题',
          color: '#409EFF'
        },
        {
          title: '及格分',
          value: this.examResult.passingScore || 60,
          unit: '分',
          color: '#67C23A'
        },
        {
          title: '得分率',
          value: Math.round((this.examResult.totalScore / 100) * 100),
          unit: '%',
          color: '#E6A23C'
        }
      ]
    },
    
    validQuestions() {
      // 过滤掉null值
      return this.examResult.questions.filter(q => q !== null);
    }
  },

  methods: {
    async loadExamResult() {
      try {
        this.loading = true;
        const recordId = this.$route.query.recordId;
        console.log('开始加载考试结果，recordId:', recordId);
        
        const res = await this.$request.get('/exam/result/' + recordId);
        console.log('考试结果API响应:', res);
        
        if (res.code === '200') {
          // 过滤掉null值的题目
          const validQuestions = res.data.questions.filter(q => q !== null);
          
          const questions = validQuestions.map(question => {
            const answer = res.data.answers.find(a => a.questionId === question.id);
            return {
              ...question,
              userAnswer: answer?.answer || (question.type === 'multiple' ? '' : ''),
              score: answer?.score || 0,
              analysis: answer?.analysis || '',
              isCorrect: answer?.isCorrect,
              fullScore: question.score || 0
            };
          });
          
          const startTime = new Date(res.data.record.startTime).getTime();
          const endTime = new Date(res.data.record.endTime).getTime();
          const duration = Math.floor((endTime - startTime) / 1000);
          
          console.log('考试记录数据:', res.data.record);
          
          // 从考试详情中获取及格分
          let passingScore = 60; // 默认及格分
          if (res.data.exam && res.data.exam.passingScore) {
            console.log('从考试详情中获取及格分:', res.data.exam.passingScore);
            passingScore = res.data.exam.passingScore;
          }
          
          this.examResult = {
            ...res.data.record,
            questions: questions,
            duration: duration,
            passingScore: passingScore,
            passed: (res.data.record.totalScore >= passingScore)
          };
          
          console.log('处理后的考试结果:', this.examResult);
          
          // 保存考试ID，用于获取考试详情
          if (res.data.record.examId) {
            console.log('从考试记录中获取到examId:', res.data.record.examId);
            this.examResult.examId = res.data.record.examId;
            // 确保在Vue的下一个tick执行，以确保examId已更新
            this.$nextTick(() => {
              this.loadExamDetail();
            });
          } else {
            console.error('考试记录中未找到examId');
          }
        } else {
          this.$message.error(res.msg || '获取考试结果失败');
        }
      } catch (error) {
        console.error('加载考试结果失败:', error);
        this.$message.error('加载考试结果失败');
      } finally {
        this.loading = false;
      }
    },

    // 获取考试详情，包含及格分信息
    async loadExamDetail() {
      // 检查是否有考试ID，没有则尝试从URL获取
      if (!this.examResult.examId && this.$route.query.examId) {
        console.log('从URL获取考试ID:', this.$route.query.examId);
        this.examResult.examId = this.$route.query.examId;
      }
      
      if (!this.examResult.examId) {
        console.error('无考试ID，无法获取考试详情');
        return;
      }
      
      console.log('开始获取考试详情，examId:', this.examResult.examId);
      
      try {
        const res = await this.$request.get('/exam/detail/' + this.examResult.examId);
        console.log('考试详情API响应:', res);
        
        if (res.code === '200') {
          this.examDetail = res.data;
          console.log('成功获取考试详情:', this.examDetail);
          
          // 更新及格分
          if (res.data.exam && res.data.exam.passingScore) {
            console.log('更新及格分:', res.data.exam.passingScore);
            this.examResult.passingScore = res.data.exam.passingScore;
            // 重新计算是否及格
            this.examResult.passed = (this.examResult.totalScore >= this.examResult.passingScore);
          } else {
            console.warn('考试详情中没有找到及格分信息');
          }
        } else {
          console.error('获取考试详情API返回错误:', res.msg || '未知错误');
        }
      } catch (error) {
        console.error('获取考试详情失败:', error);
      }
    },

    getQuestionTypeName(type) {
      const typeMap = {
        'single': '单选题',
        'multiple': '多选题',
        'judge': '判断题',
        'essay': '简答题',
        'case': '病例分析'
      };
      return typeMap[type] || type;
    },

    formatDuration(seconds) {
      if (!seconds) return '0小时0分0秒';
      
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      
      let result = '';
      if (hours > 0) result += `${hours}小时`;
      if (minutes > 0) result += `${minutes}分`;
      if (secs > 0) result += `${secs}秒`;
      
      return result || '0秒';
    },

    isCorrectOption(question, option) {
      return question.correctAnswer && question.correctAnswer.includes(option);
    },

    isWrongOption(question, option) {
      return question.userAnswer && question.userAnswer.includes(option) && 
             question.correctAnswer && !question.correctAnswer.includes(option);
    },

    isSelectedOption(question, option) {
      return question.userAnswer && 
             Array.isArray(question.userAnswer.split(',')) && 
             question.userAnswer.split(',').filter(Boolean).length > 0 && 
             question.userAnswer.includes(option);
    },

    retakeExam() {
      console.log('重新考试，传递考试ID:', this.examResult.examId);
      this.$router.push({
        path: '/front/examPage',
        query: { examId: this.examResult.examId }
      });
    },

    getScoreClass(question) {
      const maxScore = question.type === 'essay' ? 15 : 20;
      const scorePercentage = (question.score / maxScore) * 100;
      
      if (scorePercentage >= 80) return 'score-excellent';
      if (scorePercentage >= 60) return 'score-good';
      return 'score-poor';
    },

    getUserAnswer(question, subIndex) {
      if (!question.userAnswer) return '';
      const answers = question.userAnswer.split('||');
      return answers[subIndex] || '';
    }
  },

  created() {
    console.log('组件创建，加载考试结果');
    this.loadExamResult().then(() => {
      // 如果没有成功加载到考试ID，在结果加载完成后单独尝试加载考试详情
      if (!this.examResult.examId && this.$route.query.examId) {
        console.log('考试结果加载完成后尝试加载考试详情');
        this.loadExamDetail();
      }
    });
  }
}
</script>

<style scoped>
.exam-result-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.result-overview {
  background: white;
  border-radius: 8px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.score-section {
  display: flex;
  align-items: center;
  gap: 40px;
}

.total-score {
  text-align: center;
}

.score-value {
  font-size: 48px;
  font-weight: bold;
  color: #409EFF;
}

.score-label {
  color: #909399;
  margin-top: 5px;
}

.score-divider {
  width: 1px;
  height: 80px;
  background: #EBEEF5;
}

.score-details {
  flex: 1;
}

.detail-item {
  margin-bottom: 10px;
  color: #606266;
}

.detail-item .value {
  margin-left: 10px;
  color: #303133;
}

.detail-item .value.passed {
  color: #67C23A;
}

.statistics-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.stat-box {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-title {
  color: #909399;
  margin-bottom: 10px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
}

.stat-unit {
  margin-left: 5px;
  color: #909399;
}

.questions-detail {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.question-item {
  border-bottom: 1px solid #EBEEF5;
  padding: 20px 0;
}

.question-item:last-child {
  border-bottom: none;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.question-type {
  padding: 4px 12px;
  border-radius: 4px;
  color: white;
  font-size: 0.9em;
}

.question-type.single { background-color: #409EFF; }
.question-type.multiple { background-color: #67C23A; }
.question-type.judge { background-color: #E6A23C; }
.question-type.essay { background-color: #909399; }
.question-type.case { background-color: #FF9900; }

.question-score {
  color: #909399;
}

.score-full {
  color: #67C23A;
}

.question-text {
  font-size: 1.1em;
  line-height: 1.6;
  color: #303133;
  margin-bottom: 15px;
}

.options-list {
  margin-bottom: 15px;
}

.option-item {
  padding: 10px 15px;
  margin-bottom: 10px;
  border-radius: 4px;
  background: #f8f9fa;
}

.option-item.correct {
  background: #f0f9eb;
  color: #67C23A;
}

.option-item.wrong {
  background: #fef0f0;
  color: #F56C6C;
}

.option-item.selected {
  border: 1px solid currentColor;
}

.answer-section {
  margin: 15px 0;
}

.answer-label {
  font-weight: 500;
  color: #606266;
  margin-bottom: 5px;
}

.answer-content {
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  line-height: 1.6;
}

.question-analysis {
  margin-top: 15px;
  padding: 15px;
  background: #fdf6ec;
  border-radius: 4px;
}

.analysis-title {
  color: #E6A23C;
  font-weight: 500;
  margin-bottom: 5px;
}

.analysis-content {
  color: #606266;
  line-height: 1.6;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
}

.action-buttons .el-button {
  margin: 0 10px;
  padding: 12px 30px;
}

.ai-analysis {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.analysis-title {
  font-weight: 500;
  color: #409EFF;
}

.ai-score {
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 4px;
}

.score-excellent {
  color: #67C23A;
  background: #f0f9eb;
}

.score-good {
  color: #E6A23C;
  background: #fdf6ec;
}

.score-poor {
  color: #F56C6C;
  background: #fef0f0;
}

.case-text {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #EBEEF5;
}

.case-title {
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 10px;
}

.case-content {
  color: #606266;
  line-height: 1.6;
  white-space: pre-wrap;
}

.answer-section {
  margin: 15px 0;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.answer-label {
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.answer-content {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  line-height: 1.6;
  color: #303133;
  border: 1px solid #EBEEF5;
}

.analysis-content {
  color: #606266;
  line-height: 1.8;
  white-space: pre-line;
}

.sub-question-answer {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #EBEEF5;
}

.sub-question-title {
  font-weight: 500;
  color: #409EFF;
  margin-bottom: 12px;
}
</style> 