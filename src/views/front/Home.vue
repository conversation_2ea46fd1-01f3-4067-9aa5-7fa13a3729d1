<template>
  <div class="main-content">
    <div class="content-wrapper">
      <!-- 导航栏 -->
      <div class="nav-header">
        <div class="nav-left">
          <div class="brand">
            <i class="el-icon-reading"></i>
            <span>中医传承·经典讲堂</span>
          </div>
          <div class="nav-menu">
            <el-button class="menu-btn" :class="{ active: type === 'VIDEO' }" @click="initValue('VIDEO')">
              <i class="el-icon-video-camera"></i>视频教学
            </el-button>
            <el-button class="menu-btn" :class="{ active: type === 'TEXT' }" @click="initValue('TEXT')">
              <i class="el-icon-reading"></i>经典导读
            </el-button>
            <el-button class="menu-btn" :class="{ active: type === 'SCORE' }" @click="initValue('SCORE')">
              <i class="el-icon-medal"></i>积分兑换
            </el-button>
          </div>
        </div>
        <div class="sign-box">
          <el-button class="sign-btn" @click="signin">
            <i class="el-icon-brush"></i>每日打卡
          </el-button>
          <span class="sign-time" v-if="signInData?.time">上次打卡：{{ signInData.time }}</span>
        </div>
      </div>

      <!-- 精选推荐 -->
      <div class="section-box">
        <div class="section-title">
          <i class="el-icon-collection-tag"></i>
          <span class="title-text">精选推荐</span>
        </div>
        <div class="recommend-container">
          <div class="recommend-main" @click="navTo(recommend.id)">
            <div class="main-image">
              <img :src="recommend.img" :alt="recommend.name">
            </div>
            <div class="main-info">
              <div class="recommend-tag">
                <i class="el-icon-star-on"></i>
                推荐课程
              </div>
              <h3>{{recommend.name}}</h3>
            </div>
          </div>
          <div class="course-grid">
            <div v-for="item in rightData" 
                 :key="item.id" 
                 class="course-item"
                 @click="navTo(item.id)">
              <div class="item-image">
                <img :src="item.img" :alt="item.name">
              </div>
              <div class="item-info">{{item.name}}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中医典籍 -->
      <div class="section-box">
        <div class="section-title">
          <i class="el-icon-collection"></i>
          <span class="title-text">中医典籍</span>
        </div>
        <div class="recommend-container books-container">
          <div class="book-grid">
            <div v-for="item in leftData" 
                 :key="item.id" 
                 class="book-item"
                 @click="navToInformation(item.id)">
              <div class="item-image">
                <img :src="item.img" :alt="item.name">
              </div>
              <div class="item-info">{{item.name}}</div>
            </div>
          </div>
          <div class="recommend-main" @click="navToInformation(fileRecommend?.id)">
            <div class="main-image">
              <img :src="fileRecommend?.img" :alt="fileRecommend?.name">
            </div>
            <div class="main-info">
              <div class="recommend-tag">
                <i class="el-icon-collection-tag"></i>
                典藏推荐
              </div>
              <h3>{{fileRecommend?.name}}</h3>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- AI聊天助手容器 -->
    <div class="ai-container">
      <!-- AI聊天对话框 -->
      <div class="ai-chat-container" :class="{ 'show': showAiChat }">
        <div class="chat-header">
          <span class="chat-title">华小佗智能问诊</span>
          <i class="el-icon-close close-btn" @click="toggleAiChat"></i>
        </div>
         
        <!-- 嵌入AI聊天组件 -->
        <ai-component ref="aiComponent"></ai-component>
      </div>

      <!-- AI助手小人物图标 -->
      <div class="ai-assistant-icon" @click="toggleAiChat" :class="{ 'minimized': showAiChat }">
        <!-- 欢迎气泡 - 定时显示 -->
        <div class="welcome-bubble" :class="{ 'show-bubble': showBubble }">
          <div class="bubble-content">点击咨询华小佗智能助手~</div>
          <div class="bubble-arrow"></div>
        </div>
         
        <div class="icon-container">
          <img src="@/assets/imgs/ht.png" class="huatuo-image" alt="智能问诊">
          <div class="sparkle sparkle-1"></div>
          <div class="sparkle sparkle-2"></div>
          <div class="sparkle sparkle-3"></div>
           
          <div class="icon-label">华小佗</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AiComponent from './AiChat.vue';

export default {
  components: {
    AiComponent
  },
  data() {
    return {
      type: 'VIDEO',
      recommend: {},
      rightData: [],
      fileRecommend: {},
      leftData: [],
      signInData: {},
      user: JSON.parse(localStorage.getItem("xm-user") || '{}'),
      showAiChat: false,
      showBubble: false,
      bubbleTimer: null,
      chatMessages: [],
      userInput: ''
    }
  },
  mounted() {
    this.getData()
    this.getInformation()
    this.getSign()
    this.startBubbleTimer()
  },
  beforeDestroy() {
    if (this.bubbleTimer) {
      clearInterval(this.bubbleTimer)
    }
  },
  methods: {
    initValue(type) {
      this.type = type
      this.getData()
    },
    getData() {
      if ('SCORE' === this.type) {
        this.getRecommend('/score/getRecommend')
        this.getRightData('/score/getTop8')
      } else {
        this.getRecommend('/course/getRecommend?type=' + this.type)
        this.getRightData('/course/selectTop8?type=' + this.type)
      }
    },
    getRecommend(url) {
      this.$request.get(url).then(res => {
        if (res.code === '200') {
          this.recommend = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getRightData(url) {
      this.$request.get(url).then(res => {
        if (res.code === '200') {
          this.rightData = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getInformation() {
      this.$request.get('/information/getRecommend').then(res => {
        console.log('推荐资料返回数据:', res);
        if (res.code === '200') {
          this.fileRecommend = res.data;
          console.log('推荐资料:', res.data);
        } else {
          this.$message.error(res.msg);
        }
      });

      this.$request.get('/information/selectTop8').then(res => {
        console.log('资料列表返回数据:', res);
        if (res.code === '200') {
          this.leftData = res.data;
          console.log('资料列表:', res.data);
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    navTo(id) {
      if (this.type === 'SCORE') {
        location.href = '/front/scoreDetail?id=' + id
      } else {
        location.href = '/front/courseDetail?id=' + id
      }
    },
    navToInformation(id) {
      location.href = '/front/informationDetail?id=' + id
    },
    getSign() {
      this.$request.get('/signin/selectByUserId?id=' + this.user.id).then(res => {
        if (res.code === '200') {
          this.signInData = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    signin() {
      let data = {
        userId: this.user.id
      }
      this.$request.post('/signin/add', data).then(res => {
        if (res.code === '200') {
          this.$message.success('签到成功，恭喜获得10个积分')
          this.getSign()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    toggleAiChat() {
      this.showAiChat = !this.showAiChat;
      // 点击时隐藏气泡
      this.showBubble = false;
      
      // 如果是打开对话框，添加问候语
      if (this.showAiChat && this.$refs.aiComponent) {
        // 确保当没有消息时才显示问候语
        if (this.$refs.aiComponent.messages.length === 0) {
          this.$refs.aiComponent.addGreeting();
        }
      }
    },
    startBubbleTimer() {
      // 页面加载2秒后显示气泡
      setTimeout(() => {
        this.showBubble = true;
        
        // 显示5秒后隐藏
        setTimeout(() => {
          this.showBubble = false;
        }, 5000);
        
        // 然后每隔45秒显示5秒
        this.bubbleTimer = setInterval(() => {
          // 如果对话框未打开，才显示气泡
          if (!this.showAiChat) {
            this.showBubble = true;
            
            setTimeout(() => {
              this.showBubble = false;
            }, 5000);
          }
        }, 45000);
      }, 2000);
    }
  }
}
</script>

<style scoped>
/* 配色变量 */
:root {
  --brown-primary: #8B4513;
  --brown-light: #d4a17d;
  --brown-dark: #593113;
  --brown-bg: #f9f6f1;
  --brown-border: #e8e0d5;
}

/* 整体布局 */
.main-content {
  background: #f9f6f1;
  min-height: calc(100vh - 140px);
  padding: 20px 0;
  position: relative;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 导航栏样式 */
.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  margin-bottom: 30px;
  background: #fff;
  box-shadow: 0 2px 12px rgba(139, 69, 19, 0.08);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 40px;
}

.brand {
  font-size: 20px;
  color: #593113;
  font-family: "STKaiti", "楷体", serif;
  letter-spacing: 1px;
}

.nav-menu {
  display: flex;
  gap: 15px;
}

.menu-btn {
  padding: 8px 15px;
  font-size: 13px;
  color: #593113;
  background: transparent;
  border: 1px solid transparent;
  transition: all 0.3s;
  border-radius: 4px;
}

.menu-btn:hover {
  color: #8B4513;
  border-color: rgba(139, 69, 19, 0.1);
}

.menu-btn.active {
  color: #fff;
  background: linear-gradient(135deg, #8B4513, #a65d2e);
  border: none;
  box-shadow: 0 2px 6px rgba(139, 69, 19, 0.2);
}

.sign-btn {
  background: linear-gradient(135deg, #8B4513, #a65d2e);
  border: none;
  padding: 10px 20px;
  font-size: 13px;
  color: #fff;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(139, 69, 19, 0.15);
  border-radius: 4px;
}

.sign-btn:hover {
  transform: scale(1.05);
}

.sign-btn i {
  margin-right: 6px;
}

.sign-time {
  color: #8B4513;
  font-size: 13px;
  padding: 4px 8px;
  background: rgba(139, 69, 19, 0.05);
  border-radius: 3px;
}

/* 内容区块 */
.section-box {
  background: #fff;
  padding: 20px;
  margin-bottom: 40px;
  box-shadow: 0 2px 8px rgba(139, 69, 19, 0.08);
}

.section-title {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  font-size: 18px;
  color: #8B4513;
}

.title-text {
  font-size: 16px;
  color: #593113;
  font-family: "STSong", "宋体", serif;
}

/* 推荐区域 */
.recommend-container {
  display: grid;
  grid-template-columns: 240px 1fr;
  gap: 20px;
}

.recommend-main {
  height: 280px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(139, 69, 19, 0.1);
}

.main-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  background: linear-gradient(transparent, rgba(89, 49, 19, 0.9));
}

.recommend-tag {
  display: inline-block;
  padding: 4px 10px;
  font-size: 14px;
  color: #fff;
  background: rgba(139, 69, 19, 0.8);
  margin-bottom: 6px;
}

.recommend-tag i {
  margin-right: 4px;
}

.main-info h3 {
  font-size: 14px;
  color: #fff;
  margin: 0;
  font-weight: bold;
}

/* 课程和典籍网格 */
.course-grid, .books-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
}

.course-item, .book-item {
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(139, 69, 19, 0.08);
}

.item-image {
  height: 120px;
  overflow: hidden;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  transition: transform 0.3s;
}

.item-info {
  padding: 10px;
  font-size: 13px;
  color: #593113;
  background: #fff;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 悬浮效果 */
.course-item:hover, .book-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 69, 19, 0.15);
}

.course-item:hover .item-image img,
.book-item:hover .item-image img,
.recommend-main:hover .main-image img {
  transform: scale(1.05);
}

/* 中医典籍部分样式调整 */
.books-container {
  display: grid;
  grid-template-columns: 1fr 240px; /* 与精选推荐相反，右侧推荐 */
  gap: 20px;
}

.book-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 保持4列一致 */
  gap: 15px;
}

.book-item {
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(139, 69, 19, 0.08);
}

.book-item .item-image {
  height: 120px; /* 与上方保持一致的图片高度 */
  overflow: hidden;
}

/* AI容器 */
.ai-container {
  position: relative;
  z-index: 990;
}

/* AI助手图标样式 - 使用华佗图片 */
.ai-assistant-icon {
  position: fixed;
  right: 40px;
  bottom: 30px;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-origin: right bottom;
  animation: iconEnter 1s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

@keyframes iconEnter {
  0% {
    opacity: 0;
    transform: translateY(40px) scale(0.7);
  }
  60% {
    transform: translateY(-10px) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 当对话框打开时，缩小并移动华佗图标 */
.ai-assistant-icon.minimized {
  transform: scale(0.65) translateX(90px) translateY(50px);
  opacity: 0.9;
  filter: brightness(0.95);
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: floatMinimized 3s ease-in-out infinite;
  pointer-events: auto;
}

@keyframes floatMinimized {
  0%, 100% { 
    transform: scale(0.65) translateX(90px) translateY(50px);
  }
  50% { 
    transform: scale(0.65) translateX(90px) translateY(40px);
  }
}

.ai-assistant-icon.minimized .icon-container {
  transform: scale(0.9);
  transition: transform 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.ai-assistant-icon.minimized .sparkle {
  opacity: 0.4;
  transition: opacity 0.5s ease;
}

.ai-assistant-icon.minimized .icon-label {
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.4s ease;
}

.icon-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 华佗图片样式 */
.huatuo-image {
  width: 130px;
  height: 130px;
  object-fit: contain;
  transition: all 0.4s ease;
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.15));
  z-index: 2;
  animation: float 3s ease-in-out infinite, headTilt 8s ease-in-out infinite;
  transform-origin: center bottom;
  transform: scaleX(1);
}

.icon-label {
  font-size: 14px;
  color: #8B4513;
  background: rgba(255, 255, 255, 0.95);
  padding: 3px 10px;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(139, 69, 19, 0.15);
  animation: pulse 4s ease-in-out infinite;
  position: relative;
  z-index: 2;
  text-align: center;
  margin-top: 5px;
  line-height: 1.2;
  width: 70px;
  transition: all 0.3s ease;
}

.ai-assistant-icon:hover .icon-label {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(139, 69, 19, 0.2);
}

.ai-assistant-icon:hover .huatuo-image {
  transform: scale(1.05) translateY(-5px);
  filter: drop-shadow(0 6px 12px rgba(0,0,0,0.2));
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 小星星效果 */
.sparkle {
  position: absolute;
  width: 15px;
  height: 15px;
  background: #FFD700;
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
  z-index: 1;
  opacity: 0.8;
  filter: drop-shadow(0 0 3px rgba(255, 215, 0, 0.5));
}

.sparkle-1 {
  top: 10px;
  right: 20px;
  animation: twinkle 2s ease-in-out infinite, moveAround 8s linear infinite;
}

.sparkle-2 {
  top: 30px;
  right: 30px;
  width: 10px;
  height: 10px;
  animation: twinkle 2.5s ease-in-out infinite 0.4s, moveAround 6s linear infinite 1s;
}

.sparkle-3 {
  top: 15px;
  left: 30px;
  width: 12px;
  height: 12px;
  animation: twinkle 3s ease-in-out infinite 0.8s, moveAround 7s linear infinite 0.5s;
}

/* 闪烁动画 */
@keyframes twinkle {
  0%, 100% { 
    opacity: 0.3;
    transform: scale(0.8) rotate(-15deg);
    filter: brightness(0.8);
  }
  50% { 
    opacity: 1;
    transform: scale(1.2) rotate(15deg);
    filter: brightness(1.2);
  }
}

/* 星星围绕动画 */
@keyframes moveAround {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    transform: translate(5px, 5px) rotate(90deg);
  }
  50% {
    transform: translate(0, 10px) rotate(180deg);
  }
  75% {
    transform: translate(-5px, 5px) rotate(270deg);
  }
  100% {
    transform: translate(0, 0) rotate(360deg);
  }
}

/* 漂浮动画 */
@keyframes float {
  0%, 100% { 
    transform: translateY(0);
  }
  50% { 
    transform: translateY(-8px);
  }
}

/* 头部轻微摇晃 */
@keyframes headTilt {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-5deg);
  }
  75% {
    transform: rotate(5deg);
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 6px rgba(139, 69, 19, 0.15);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 3px 8px rgba(139, 69, 19, 0.25);
  }
}

/* 欢迎气泡样式 - 定时显示 */
.welcome-bubble {
  position: absolute;
  top: -60px;
  right: 0;
  background: white;
  padding: 12px 18px;
  border-radius: 15px;
  box-shadow: 0 3px 15px rgba(0,0,0,0.15);
  width: 220px;
  opacity: 0;
  visibility: hidden;
  color: #593113;
  font-size: 14px;
  z-index: 1000;
  transition: all 0.5s ease;
  transform: translateY(10px);
}

.welcome-bubble.show-bubble {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  animation: bobble 3s ease-in-out infinite;
}

.bubble-content {
  position: relative;
  animation: textFade 8s infinite;
  word-break: break-word;
  white-space: normal;
  text-align: center;
  line-height: 1.4;
}

.bubble-arrow {
  position: absolute;
  bottom: -8px;
  right: 100px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
  filter: drop-shadow(0 2px 2px rgba(0,0,0,0.05));
}

@keyframes bobble {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes textFade {
  0%, 100% {
    opacity: 1;
  }
  45%, 55% {
    opacity: 0.7;
  }
}

/* 对话框样式调整 */
.ai-chat-container {
  position: fixed;
  right: -400px;
  bottom: 100px;
  width: 360px;
  height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.15);
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  z-index: 997; /* 降低一点，确保不会盖住图标 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: translateX(50px) scale(0.95);
  opacity: 0;
  transform-origin: bottom right;
  pointer-events: none;
}

.ai-chat-container.show {
  right: 40px;
  transform: translateX(0) scale(1);
  opacity: 1;
  pointer-events: auto;
}

/* 修复在某些情况下连接线可能重叠的问题 */
.ai-chat-container.show::after {
  content: '';
  position: absolute;
  bottom: -15px;
  right: 80px;
  width: 0;
  height: 15px;
  border-right: 2px dashed rgba(139, 69, 19, 0.3);
  z-index: -1;
}

/* 确保聊天组件占满容器 */
.ai-chat-container > :last-child {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 聊天框打开时的焦点动画 */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #D9A85C, #8B4513);
  color: white;
  border-radius: 12px 12px 0 0;
  position: relative;
  overflow: hidden;
}

.chat-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.ai-chat-container.show .chat-header::after {
  opacity: 1;
  animation: headerGlow 2s ease-in-out infinite alternate;
}

@keyframes headerGlow {
  from {
    opacity: 0.2;
    transform: scale(0.9);
  }
  to {
    opacity: 0.4;
    transform: scale(1.1);
  }
}

.chat-title {
  font-weight: bold;
  font-size: 16px;
  letter-spacing: 0.5px;
}

.close-btn {
  cursor: pointer;
  font-size: 18px;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.1);
  z-index: 1000;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg) scale(1.1);
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  display: flex;
  flex-direction: column;
  background-color: #f6f7f9;
}

.message {
  margin-bottom: 12px;
  max-width: 80%;
  padding: 10px 12px;
  border-radius: 15px;
  position: relative;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message.received {
  align-self: flex-start;
  background-color: white;
  border: 1px solid #e8e8e8;
  border-top-left-radius: 5px;
}

.message.sent {
  align-self: flex-end;
  background-color: #3a8ee6;
  color: white;
  border-top-right-radius: 5px;
}

.message-content {
  font-size: 14px;
  line-height: 1.5;
}

.message-time {
  font-size: 10px;
  opacity: 0.7;
  margin-top: 4px;
  text-align: right;
}

.chat-input {
  display: flex;
  padding: 12px;
  background-color: white;
  border-top: 1px solid #eaeaea;
}

.chat-input .el-input {
  margin-right: 8px;
}

.chat-input .el-button {
  padding: 9px 15px;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .book-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .books-container, .recommend-container {
    grid-template-columns: 1fr;
  }
  
  .recommend-main {
    height: 200px;
  }
  
  .book-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .ai-assistant-icon.minimized {
    transform: scale(0.5) translateX(60px) translateY(40px);
  }
  
  @keyframes floatMinimized {
    0%, 100% { 
      transform: scale(0.5) translateX(60px) translateY(40px);
    }
    50% { 
      transform: scale(0.5) translateX(60px) translateY(30px);
    }
  }
  
  .ai-chat-container {
    width: 90%;
    bottom: 80px;
    right: -100%;
  }
  
  .ai-chat-container.show {
    right: 5%;
    bottom: 80px;
  }
  
  .ai-assistant-icon {
    right: 20px;
    bottom: 20px;
  }
}

/* 在对话框关闭时添加光效 */
.ai-chat-container.show + .ai-assistant-icon::before {
  content: '';
  position: absolute;
  top: -10px;
  right: -10px;
  left: -10px;
  bottom: -10px;
  background: radial-gradient(circle, rgba(219, 166, 92, 0.2) 0%, rgba(219, 166, 92, 0) 70%);
  z-index: -1;
  opacity: 0;
  border-radius: 50%;
  animation: glowPulse 2s ease-in-out infinite;
}

@keyframes glowPulse {
  0%, 100% {
    opacity: 0;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}
</style>



