<template>
  <div class="main-content">
    <div class="main-content-wrapper">
      <div class="search-section">
        <div class="input-with-buttons">
          <el-input placeholder="请输入课程名称" size="mini" v-model="name" class="search-input"></el-input>
          <el-button type="info" plain size="mini" @click="load(1)" class="search-btn">查询</el-button>
          <el-button type="warning" plain size="mini" @click="reset" class="reset-btn">重置</el-button>
        </div>
      </div>
      <div class="table">
        <el-table :data="tableData" stripe>
          <el-table-column prop="id" label="序号" width="80" align="center" sortable></el-table-column>
          <el-table-column prop="img" label="课程封面" show-overflow-tooltip width="100">
            <template v-slot="scope">
              <div class="course-cover">
                <el-image class="course-image" v-if="scope.row.img" :src="scope.row.img" :preview-src-list="[scope.row.img]"></el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="课程名称" show-overflow-tooltip width="400">
            <template v-slot="scope">
              <a :href="'/front/scoreDetail?id=' + scope.row.id">{{ scope.row.name }}</a>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="课程类型">
            <template v-slot="scope">
              <span v-if="scope.row.type === 'VIDEO'" class="video-course">视频课</span>
              <span v-else class="text-course">图文课</span>
            </template>
          </el-table-column>
          <el-table-column prop="price" label="所需积分">
            <template v-slot="scope">
              <span class="paid-course" v-if="scope.row.price > 0">{{ scope.row.price }} 积分</span>
              <span class="free-course" v-else>公开课</span>
            </template>
          </el-table-column>
          <el-table-column prop="time" label="发布时间"></el-table-column>
        </el-table>

        <div class="pagination-wrapper">
          <el-pagination
              background
              @current-change="handleCurrentChange"
              :current-page="pageNum"
              :page-sizes="[5, 10, 20]"
              :page-size="pageSize"
              layout="total, prev, pager, next"
              :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {

  data() {
    return {
      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
      tableData: [],
      pageNum: 1,
      pageSize: 8,
      total: 0,
      name: null,
    }
  },
  mounted() {
    this.load(1)
  },
  // methods：本页面所有的点击事件或者其他函数定义区
  methods: {
    load(pageNum) {  // 分页查询
      if (pageNum) this.pageNum = pageNum
      this.$request.get('/score/selectPage', {
        params: {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          name: this.name,
        }
      }).then(res => {
        this.tableData = res.data?.list
        this.total = res.data?.total
      })
    },
    reset() {
      this.name = null
      this.load(1)
    },
    handleCurrentChange(pageNum) {
      this.load(pageNum)
    },
  }
}
</script>
<style scoped>
 .main-content-wrapper {
  width: 70%;
  margin: 30px auto;
}

.search-section {
  margin-bottom: 20px;
}
.search-input{
  width: 200px;
}

.input-with-buttons input {
  width: 20px;
}

.input-with-buttons button {
  margin-left: 10px;
}

.course-cover {
  display: flex;
  align-items: center;
}

.course-image {
  width: 60px;
  height: 40px;
  border-radius: 5px;
  border: 1px solid #cccccc;
}

.pagination-wrapper {
  margin-top: 20px;
}

.video-course {
  color: #b67259;
}

.text-course {
  color: #448231;
}

.paid-course {
  color: red;
}

.free-course {
  color: green;
}
</style>
```
