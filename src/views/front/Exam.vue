<script>
export default {
  data() {
    return {
      recordId: null,
      examInfo: null,
      questions: [],
      answers: {},
      loading: true,
      timer: null,
      remainingTime: 0
    }
  },
  async created() {
    // 从路由参数获取考试记录ID
    this.recordId = this.$route.query.recordId;
    if (!this.recordId) {
      this.$message.error('无效的考试记录');
      this.$router.push('/test');
      return;
    }
    
    await this.loadExamData();
    this.startTimer();
  },
  methods: {
    async loadExamData() {
      try {
        // 获取考试题目
        const res = await this.$request.get('/exam/questions', {
          params: { examId: this.examInfo.examId }
        });
        
        if (res.code === '200') {
          this.questions = res.data;
          // 初始化答案对象
          this.questions.forEach(q => {
            this.answers[q.id] = '';
          });
        }
      } catch (error) {
        this.$message.error('加载考试数据失败');
      } finally {
        this.loading = false;
      }
    },
    
    // 其他方法保持不变...
  }
}
</script> 