<template>
  <div class="main-content">
    <div class="main-container">
      <div class="course-title">我兑换过的课程（{{total}}）</div>
      <div class="table">
        <el-table :data="tableData" stripe>
          <el-table-column prop="id" label="序号" width="80" align="center" sortable></el-table-column>
          <el-table-column prop="scoreImg" label="课程封面" width="100">
            <template v-slot="scope">
              <div class="course-image-container">
                <el-image class="course-image" v-if="scope.row.scoreImg" :src="scope.row.scoreImg" :preview-src-list="[scope.row.scoreImg]"></el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="courseName" label="课程名称" width="150" show-overflow-tooltip>
            <template v-slot="scope">
              <a :href="'/front/scoreDetail?id=' + scope.row.scoreId">{{ scope.row.scoreName }}</a>
            </template>
          </el-table-column>
          <el-table-column prop="score" label="兑换积分" width="100"></el-table-column>
          <el-table-column prop="orderId" label="订单编号" width="200"></el-table-column>
          <el-table-column prop="time" label="兑换时间" width="200"></el-table-column>
        </el-table>

        <div class="pagination">
          <el-pagination
              background
              @current-change="handleCurrentChange"
              :current-page="pageNum"
              :page-sizes="[5, 10, 20]"
              :page-size="pageSize"
              layout="total, prev, pager, next"
              :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {

  data() {
    return {
      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
      tableData: [],
      pageNum: 1,
      pageSize: 6,
      total: 0,
      name: null,
    }
  },
  mounted() {
    this.load(1)
  },
  methods: {
    load(pageNum) {  // 分页查询
      if (pageNum) this.pageNum = pageNum
      this.$request.get('/scoreorder/selectPage', {
        params: {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          userId: this.user.id,
        }
      }).then(res => {
        this.tableData = res.data?.list
        this.total = res.data?.total
      })
    },
    reset() {
      this.name = null
      this.load(1)
    },
    handleCurrentChange(pageNum) {
      this.load(pageNum)
    },
  }
}
</script>
<style scoped>
.main-container {
  width: 70%;
  margin: 30px auto;
}

.course-title {
  margin: 20px 0;
}

.course-image-container {
  display: flex;
  align-items: center;
}

.course-image {
  width: 60px;
  height: 40px;
  border-radius: 10px;
}

.pagination {
  margin-top: 20px;
}

</style>
```
