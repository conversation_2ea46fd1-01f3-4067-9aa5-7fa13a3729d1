<template>
  <div class="front-container">
    <!-- 公告栏 -->
    <div class="announcement-bar">
      <i class="el-icon-bell"></i>
      <span class="notice-text">{{ top }}</span>
    </div>
    
    <!-- 头部导航 -->
    <div class="header">
      <div class="header-content">
        <!-- 左侧Logo -->
        <div class="logo-area">
          <img src="@/assets/imgs/logo.png" alt="logo">
          <div class="brand-name">中医传承</div>
        </div>

        <!-- 中间导航菜单 -->
        <div class="nav-area">
          <el-menu :default-active="$route.path" mode="horizontal" router>
            <el-menu-item index="/front/home">
              <i class="el-icon-s-home"></i>首页
            </el-menu-item>
            <el-menu-item index="/front/course">
              <i class="el-icon-reading"></i>课程学习
            </el-menu-item>
            <el-menu-item index="/front/test">
              <i class="el-icon-edit-outline"></i>知识测试
            </el-menu-item>
            <el-menu-item index="/front/score">
              <i class="el-icon-medal"></i>积分兑换
            </el-menu-item>
            <el-menu-item index="/front/information">
              <i class="el-icon-collection"></i>典籍资源
            </el-menu-item>
            <el-menu-item index="/front/myInfo">
              <i class="el-icon-upload"></i>资料分享
            </el-menu-item>
          </el-menu>
        </div>

        <!-- 右侧用户区域 -->
        <div class="user-area">
          <template v-if="!user.username">
            <el-button class="auth-btn login" @click="$router.push('/login')">登录</el-button>
            <el-button class="auth-btn register" @click="$router.push('/register')">注册</el-button>
          </template>
          <el-dropdown v-else>
            <div class="user-info">
              <img :src="user.avatar" alt="avatar">
              <span class="username">{{ user.name }}</span>
              <i class="el-icon-arrow-down"></i>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="navTo('/front/person')">
                <i class="el-icon-user"></i>个人中心
              </el-dropdown-item>
              <el-dropdown-item @click.native="navTo('/front/scoreOrder')">
                <i class="el-icon-goods"></i>我的兑换
              </el-dropdown-item>
              <el-dropdown-item @click.native="navTo('/front/FileOrder')">
                <i class="el-icon-document"></i>资源兑换
              </el-dropdown-item>
              <el-dropdown-item @click.native="logout">
                <i class="el-icon-switch-button"></i>退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="main-body">
      <router-view ref="child" @update:user="updateUser" />
    </div>

    <!-- 页脚 -->
    <footer class="footer">
      <p>© 2024 中医传承平台 · 传承国粹 · 弘扬中医</p>
    </footer>
  </div>
</template>

<script>

export default {
  name: "FrontLayout",

  data() {
    return {
      top: '',
      notice: [],
      user: JSON.parse(localStorage.getItem("xm-user") || '{}'),
      noticeInterval: null,
    }
  },

  mounted() {
    this.loadNotice()
  },

  beforeDestroy() {
    if (this.noticeInterval) {
      clearInterval(this.noticeInterval);
    }
  },

  methods: {
    loadNotice() {
      this.$request.get('/notice/selectAll').then(res => {
        this.notice = res.data
        let i = 0
        if (this.notice.length) {
          this.top = this.notice[0].content
          this.noticeInterval = setInterval(() => {
            this.top = this.notice[i].content
            i = (i + 1) % this.notice.length;
          }, 2500)
        }
      })
    },
    updateUser() {
      this.user = JSON.parse(localStorage.getItem('xm-user') || '{}')
    },
    logout() {
      localStorage.removeItem("xm-user");
      this.$router.push("/login");
    },
    navTo(url) {
      this.$router.push(url)
    }
  }
}

</script>

<style scoped>
.front-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.announcement-bar {
  background: #948e8e;
  padding: 10px 20px;
  color: #ffffff;
  font-size: 13px;
  border-bottom: 1px solid #4a332a;
}

.notice-text {
  margin-left: 8px;
}

.header {
  background: #2c1810;
  border-bottom: 1px solid #e8e0d5;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  height: 60px;
  padding: 0 20px;
}

.logo-area {
  display: flex;
  align-items: center;
  gap: 15px;
  width: 200px;
}

.logo-area img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
}

.brand-name {
  font-family: "STKaiti", "楷体", serif;
  font-size: 22px;
  color: #ffffff;
  font-weight: bold;
}

.nav-area {
  flex: 1;
}

.el-menu {
  border: none !important;
  background: transparent;
}

.el-menu--horizontal {
  display: flex;
  justify-content: center;
}

.el-menu--horizontal > .el-menu-item {
  height: 60px;
  line-height: 60px;
  font-family: "STKaiti", "楷体", serif;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.8) !important;
  padding: 0 20px;
  border-bottom: 2px solid transparent !important;
  transition: all 0.3s ease;
}

.el-menu--horizontal > .el-menu-item:hover {
  color: #fff !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border-bottom: 2px solid rgba(255, 255, 255, 0.3) !important;
}

.el-menu--horizontal > .el-menu-item.is-active {
  color: #fff !important;
  font-weight: bold;
  border-bottom: 2px solid #fff !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

.el-menu--horizontal > .el-menu-item i {
  margin-right: 4px;
  font-size: 16px;
}

.el-menu--horizontal > .el-menu-item:hover {
  color: #d4a17d !important;
  background: transparent !important;
}

.el-menu--horizontal > .el-menu-item.is-active {
  color: #d4a17d !important;
  font-weight: bold;
  border-bottom: 2px solid #d4a17d !important;
}

.user-area {
  width: 200px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.auth-btn {
  padding: 8px 20px;
  margin-left: 12px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.login {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.6);
  color: rgba(255, 255, 255, 0.9);
}

.login:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #fff;
  color: #fff;
}

.register {
  background: #fff;
  border: none;
  color: #2c1810;
}

.register:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.15);
}

.user-info img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
}

.username {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  margin: 0 4px;
}

.el-dropdown-menu {
  background: #2c1810;
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  padding: 6px 0;
}

.el-dropdown-menu__item {
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  padding: 10px 16px;
  transition: all 0.3s ease;
}

.el-dropdown-menu__item i {
  margin-right: 10px;
  font-size: 16px;
  opacity: 0.8;
}

.el-dropdown-menu__item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.el-dropdown-menu__item:hover i {
  opacity: 1;
}

.main-body {
  flex: 1;
  background: #f8f5f2;
  min-height: calc(100vh - 140px);
}

.footer {
  background: #2c1810;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  padding: 20px;
  font-family: "STKaiti", "楷体", serif;
  font-size: 14px;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 10px;
  }
  
  .nav-area {
    width: 100%;
    overflow-x: auto;
  }
  
  .el-menu--horizontal {
    justify-content: flex-start;
  }
  
  .user-area {
    width: 100%;
    justify-content: center;
    margin-top: 10px;
  }
}
</style>
