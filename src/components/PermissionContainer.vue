<template>
  <div v-if="hasPermission" class="permission-container">
    <slot></slot>
  </div>
  <div v-else-if="showNoPermissionTip" class="no-permission-tip">
    <el-alert
      :title="noPermissionMessage"
      type="warning"
      :closable="false"
      show-icon>
    </el-alert>
  </div>
</template>

<script>
import permissionManager from '@/utils/permission'

export default {
  name: 'PermissionContainer',
  props: {
    // 权限标识
    permission: {
      type: String,
      default: ''
    },
    // 角色标识
    role: {
      type: String,
      default: ''
    },
    // 多个权限（AND逻辑）
    permissions: {
      type: Array,
      default: () => []
    },
    // 多个角色（OR逻辑）
    roles: {
      type: Array,
      default: () => []
    },
    // 是否为管理员专用
    adminOnly: {
      type: Boolean,
      default: false
    },
    // 是否为超级管理员专用
    superAdminOnly: {
      type: Boolean,
      default: false
    },
    // 是否为用户专用
    userOnly: {
      type: <PERSON><PERSON>an,
      default: false
    },
    // 是否显示无权限提示
    showNoPermissionTip: {
      type: Boolean,
      default: false
    },
    // 无权限提示消息
    noPermissionMessage: {
      type: String,
      default: '您没有权限访问此内容'
    }
  },
  
  data() {
    return {
      hasPermission: false
    }
  },
  
  async mounted() {
    await this.checkPermission()
  },
  
  watch: {
    permission: 'checkPermission',
    role: 'checkPermission',
    permissions: 'checkPermission',
    roles: 'checkPermission',
    adminOnly: 'checkPermission',
    superAdminOnly: 'checkPermission',
    userOnly: 'checkPermission'
  },
  
  methods: {
    async checkPermission() {
      try {
        // 管理员专用检查
        if (this.adminOnly) {
          this.hasPermission = permissionManager.isAdmin()
          return
        }

        // 超级管理员专用检查
        if (this.superAdminOnly) {
          this.hasPermission = permissionManager.isSuperAdmin()
          return
        }

        // 用户专用检查
        if (this.userOnly) {
          this.hasPermission = permissionManager.isUser()
          return
        }
        
        // 多角色检查（OR逻辑）
        if (this.roles && this.roles.length > 0) {
          const results = await Promise.all(
            this.roles.map(role => permissionManager.hasRole(role))
          )
          this.hasPermission = results.some(result => result === true)
          return
        }
        
        // 多权限检查（AND逻辑）
        if (this.permissions && this.permissions.length > 0) {
          this.hasPermission = await permissionManager.hasAllPermissions(this.permissions)
          return
        }
        
        // 单个角色检查
        if (this.role) {
          this.hasPermission = await permissionManager.hasRole(this.role)
          return
        }
        
        // 单个权限检查
        if (this.permission) {
          this.hasPermission = await permissionManager.hasPermission(this.permission)
          return
        }
        
        // 没有指定任何权限条件，默认显示
        this.hasPermission = true
        
      } catch (error) {
        console.error('权限检查失败:', error)
        // 权限检查失败时隐藏内容（安全策略）
        this.hasPermission = false
      }
    }
  }
}
</script>

<style scoped>
.permission-container {
  width: 100%;
}

.no-permission-tip {
  padding: 20px;
  text-align: center;
}

.no-permission-tip .el-alert {
  max-width: 400px;
  margin: 0 auto;
}
</style>
