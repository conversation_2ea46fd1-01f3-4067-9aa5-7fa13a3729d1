/* 中医风格通用样式 */
.tcm-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #e8e0d5;
  position: relative;
}

.tcm-container::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  bottom: 8px;
  border: 1px solid #e8e0d5;
  pointer-events: none;
}

.tcm-title {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e8e0d5;
  display: flex;
  align-items: center;
  gap: 10px;
  font-family: "STKaiti", "楷体", serif;
  color: #8B4513;
}

.tcm-btn {
  padding: 8px 15px;
  border: 1px solid #d4a17d;
  background: transparent;
  color: #8B4513;
  font-family: "STKaiti", "楷体", serif;
  transition: all 0.3s;
}

.tcm-btn:hover {
  background: rgba(139, 69, 19, 0.05);
}

.tcm-btn.active {
  background: #8B4513;
  color: #fff;
  border-color: #8B4513;
}

.tcm-card {
  border-radius: 4px;
  border: 1px solid #e8e0d5;
  overflow: hidden;
  position: relative;
}

.tcm-card::after {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  border: 1px solid #e8e0d5;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s;
}

.tcm-card:hover::after {
  opacity: 1;
}

.tcm-text {
  font-family: "STKaiti", "楷体", serif;
  color: #5c3a21;
}

/* Element UI 导航菜单覆盖 */
.el-menu {
  background-color: transparent !important;
  border: none !important;
}

.el-menu--horizontal {
  border-bottom: none !important;
}

.el-menu--horizontal > .el-menu-item {
  height: 64px;
  line-height: 64px;
  color: #593113 !important;
  font-size: 15px;
  padding: 0 20px;
  border-bottom: none !important;
  font-family: "STKaiti", "楷体", serif;
  background-color: transparent !important;
}

.el-menu--horizontal > .el-menu-item i {
  color: #593113 !important;
  margin-right: 4px;
}

.el-menu--horizontal > .el-menu-item:hover {
  color: #8B4513 !important;
  background-color: rgba(139, 69, 19, 0.05) !important;
}

.el-menu--horizontal > .el-menu-item.is-active {
  color: #8B4513 !important;
  font-weight: bold;
  background-color: rgba(139, 69, 19, 0.1) !important;
  border-bottom: 2px solid #8B4513 !important;
}

/* 弹出菜单样式 */
.el-menu--popup-bottom-start {
  background-color: #fff !important;
  border: none !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1) !important;
}

.el-menu--popup-bottom-start .el-menu-item {
  height: 40px !important;
  line-height: 40px !important;
  color: #593113 !important;
  font-size: 14px !important;
  padding: 0 20px !important;
  background-color: transparent !important;
}

.el-menu--popup-bottom-start .el-menu-item:hover {
  background-color: rgba(139, 69, 19, 0.05) !important;
  color: #8B4513 !important;
}

/* 下拉菜单样式 */
.el-dropdown-menu {
  background-color: #fff !important;
  border: none !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1) !important;
  padding: 5px 0 !important;
}

.el-dropdown-menu__item {
  color: #593113 !important;
  font-size: 14px !important;
  padding: 8px 20px !important;
  font-family: "STKaiti", "楷体", serif !important;
  line-height: 1.5 !important;
}

.el-dropdown-menu__item:hover {
  background-color: rgba(139, 69, 19, 0.05) !important;
  color: #8B4513 !important;
}

.el-dropdown-menu__item i {
  color: #8B4513 !important;
  margin-right: 8px !important;
} 