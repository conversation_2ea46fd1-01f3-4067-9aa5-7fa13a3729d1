import request from './request'

/**
 * Sa-Token权限管理工具类
 * 提供权限检查、角色验证等功能
 */
class PermissionManager {
  constructor() {
    this.permissionCache = new Map()
    this.roleCache = new Map()
    this.cacheTimeout = 5 * 60 * 1000 // 5分钟缓存
  }

  /**
   * 检查用户是否有指定权限
   * @param {string} permission 权限标识
   * @returns {Promise<boolean>}
   */
  async hasPermission(permission) {
    if (!permission) return true
    
    try {
      // 检查缓存
      const cacheKey = `permission_${permission}`
      const cached = this.getFromCache(cacheKey)
      if (cached !== null) {
        return cached
      }

      // 从服务器检查
      const res = await request.get('/permission/check', {
        params: { permission }
      })
      
      const hasPermission = res.data?.hasPermission || false
      
      // 缓存结果
      this.setCache(cacheKey, hasPermission)
      
      return hasPermission
    } catch (error) {
      console.error('权限检查失败:', error)
      return false
    }
  }

  /**
   * 检查用户是否有指定角色
   * @param {string} role 角色标识
   * @returns {Promise<boolean>}
   */
  async hasRole(role) {
    if (!role) return true
    
    try {
      // 检查缓存
      const cacheKey = `role_${role}`
      const cached = this.getFromCache(cacheKey)
      if (cached !== null) {
        return cached
      }

      // 从服务器检查
      const res = await request.get('/permission/check/role', {
        params: { role }
      })
      
      const hasRole = res.data?.hasRole || false
      
      // 缓存结果
      this.setCache(cacheKey, hasRole)
      
      return hasRole
    } catch (error) {
      console.error('角色检查失败:', error)
      return false
    }
  }

  /**
   * 检查多个权限（AND逻辑）
   * @param {string[]} permissions 权限数组
   * @returns {Promise<boolean>}
   */
  async hasAllPermissions(permissions) {
    if (!permissions || permissions.length === 0) return true
    
    try {
      const results = await Promise.all(
        permissions.map(permission => this.hasPermission(permission))
      )
      return results.every(result => result === true)
    } catch (error) {
      console.error('批量权限检查失败:', error)
      return false
    }
  }

  /**
   * 检查多个权限（OR逻辑）
   * @param {string[]} permissions 权限数组
   * @returns {Promise<boolean>}
   */
  async hasAnyPermission(permissions) {
    if (!permissions || permissions.length === 0) return true
    
    try {
      const results = await Promise.all(
        permissions.map(permission => this.hasPermission(permission))
      )
      return results.some(result => result === true)
    } catch (error) {
      console.error('批量权限检查失败:', error)
      return false
    }
  }

  /**
   * 获取当前用户的所有权限和角色
   * @returns {Promise<{permissions: string[], roles: string[]}>}
   */
  async getCurrentUserPermissions() {
    try {
      const res = await request.get('/permission/current')
      return {
        permissions: res.data?.permissions || [],
        roles: res.data?.roles || []
      }
    } catch (error) {
      console.error('获取用户权限失败:', error)
      return { permissions: [], roles: [] }
    }
  }

  /**
   * 检查登录状态
   * @returns {Promise<boolean>}
   */
  async checkLoginStatus() {
    try {
      const res = await request.get('/auth/check')
      return res.data?.isLogin || false
    } catch (error) {
      console.error('检查登录状态失败:', error)
      return false
    }
  }

  /**
   * 刷新Token
   * @returns {Promise<string|null>}
   */
  async refreshToken() {
    try {
      const res = await request.post('/auth/token/refresh')
      if (res.code === '200') {
        // 更新本地存储的token
        const user = JSON.parse(localStorage.getItem('xm-user') || '{}')
        user.token = res.data.tokenValue
        localStorage.setItem('xm-user', JSON.stringify(user))
        return res.data.tokenValue
      }
      return null
    } catch (error) {
      console.error('刷新Token失败:', error)
      return null
    }
  }

  /**
   * 清除权限缓存
   */
  clearCache() {
    this.permissionCache.clear()
    this.roleCache.clear()
  }

  /**
   * 从缓存获取数据
   * @private
   */
  getFromCache(key) {
    const cached = this.permissionCache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.value
    }
    return null
  }

  /**
   * 设置缓存
   * @private
   */
  setCache(key, value) {
    this.permissionCache.set(key, {
      value,
      timestamp: Date.now()
    })
  }

  /**
   * 获取用户信息
   * @returns {Object}
   */
  getCurrentUser() {
    return JSON.parse(localStorage.getItem('xm-user') || '{}')
  }

  /**
   * 检查是否为管理员（包括超级管理员）
   * @returns {boolean}
   */
  isAdmin() {
    const user = this.getCurrentUser()
    return user.role === 'ADMIN' || user.role === 'SUPER_ADMIN'
  }

  /**
   * 检查是否为超级管理员
   * @returns {boolean}
   */
  isSuperAdmin() {
    const user = this.getCurrentUser()
    return user.role === 'SUPER_ADMIN'
  }

  /**
   * 检查是否为普通用户
   * @returns {boolean}
   */
  isUser() {
    const user = this.getCurrentUser()
    return user.role === 'USER'
  }

  /**
   * 登出
   */
  async logout() {
    try {
      await request.post('/logout')
    } catch (error) {
      console.error('登出失败:', error)
    } finally {
      // 清除本地数据
      localStorage.removeItem('xm-user')
      this.clearCache()
    }
  }
}

// 创建单例实例
const permissionManager = new PermissionManager()

// 导出实例和类
export default permissionManager
export { PermissionManager }

// 便捷方法导出
export const hasPermission = (permission) => permissionManager.hasPermission(permission)
export const hasRole = (role) => permissionManager.hasRole(role)
export const hasAllPermissions = (permissions) => permissionManager.hasAllPermissions(permissions)
export const hasAnyPermission = (permissions) => permissionManager.hasAnyPermission(permissions)
export const getCurrentUserPermissions = () => permissionManager.getCurrentUserPermissions()
export const checkLoginStatus = () => permissionManager.checkLoginStatus()
export const refreshToken = () => permissionManager.refreshToken()
export const isAdmin = () => permissionManager.isAdmin()
export const isSuperAdmin = () => permissionManager.isSuperAdmin()
export const isUser = () => permissionManager.isUser()
export const logout = () => permissionManager.logout()
