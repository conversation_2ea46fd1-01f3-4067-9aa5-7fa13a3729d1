# 🏥 中医知识在线学习与测评系统

![版本](https://img.shields.io/badge/版本-2.0.0-blue)
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.2.3-brightgreen)
![Vue](https://img.shields.io/badge/Vue-2.x-4FC08D)
![JDK版本](https://img.shields.io/badge/JDK-17-orange)
![缓存系统](https://img.shields.io/badge/缓存-多级缓存-yellow)

## 📖 项目简介

中医知识在线学习与测评系统是一个专注于中医领域的现代化教育平台，采用前后端分离架构，集成了AI智能问答、多级缓存、性能监控等先进技术，为中医学习者提供全面、高效的知识学习和能力测评服务。

### 🎯 核心特色

- **🚀 高性能架构**: 多级缓存 + 线程池优化，响应速度提升90%
- **🤖 AI智能辅助**: 集成OpenAI和讯飞星火，提供智能问答和学习建议
- **📊 实时监控**: 完整的性能监控和指标收集系统
- **🔄 异步处理**: 全面的异步日志和批量操作优化
- **💾 多级缓存**: Caffeine本地缓存 + Redis分布式缓存

## 🛠️ 技术架构

### 后端技术栈
- **核心框架**: Spring Boot 3.2.3
- **ORM框架**: MyBatis Plus 3.5.5
- **数据库**: MySQL 8.0+ (HikariCP连接池优化)
- **缓存系统**:
  - L1缓存: Caffeine (本地缓存)
  - L2缓存: Redis (分布式缓存)
- **性能监控**: Micrometer + Spring Boot Actuator
- **文档**: Knife4j 3.0.2 (Swagger增强版)
- **工具库**: Hutool、Lombok
- **文件存储**: 阿里云OSS
- **AI集成**:
  - Spring AI + OpenAI (智能问答)
  - 讯飞星火 (中医专业问答)
- **安全认证**: JWT
- **异步处理**: 自定义线程池 + 异步日志

### 前端技术栈
- **核心框架**: Vue 2.x
- **UI组件库**: Element UI
- **路由管理**: Vue Router
- **HTTP客户端**: Axios
- **状态管理**: Vuex
- **构建工具**: Vue CLI
- **图表库**: ECharts (数据可视化)

## 📁 项目结构

```
tcm-manager/
├── springboot/                    # 后端项目
│   ├── src/main/java/com/tcm/
│   │   ├── common/                # 公共组件
│   │   │   ├── aspect/            # AOP切面 (统一日志、性能监控)
│   │   │   ├── config/            # 配置类 (缓存、线程池、数据库等)
│   │   │   ├── enums/             # 枚举类
│   │   │   └── utils/             # 工具类
│   │   ├── controller/            # REST控制器
│   │   │   ├── PerformanceController.java  # 性能监控接口
│   │   │   └── ...                # 其他业务控制器
│   │   ├── service/               # 服务层
│   │   │   ├── AsyncStatsService.java      # 异步统计服务
│   │   │   ├── CacheService.java           # 多级缓存服务
│   │   │   ├── BatchOptimizationService.java # 批量操作优化
│   │   │   ├── QueryOptimizationService.java # 查询优化服务
│   │   │   ├── PerformanceMetricsService.java # 性能指标服务
│   │   │   └── impl/               # 服务实现
│   │   ├── entity/                # 实体类
│   │   ├── mapper/                # MyBatis映射器
│   │   ├── dto/                   # 数据传输对象
│   │   └── exception/             # 异常处理
│   ├── src/main/resources/
│   │   ├── mapper/                # MyBatis XML映射文件
│   │   ├── application.yml        # 主配置文件
│   │   ├── application-dev.yml    # 开发环境配置
│   │   └── logback-spring.xml     # 日志配置
│   ├── PERFORMANCE_OPTIMIZATION.md    # 性能优化文档
│   ├── LOG_OPTIMIZATION.md            # 日志优化文档
│   ├── ADVANCED_PERFORMANCE_OPTIMIZATION.md # 高级性能优化文档
│   └── pom.xml                    # Maven配置
├── vue/                           # 前端项目
│   ├── src/
│   │   ├── views/
│   │   │   ├── front/             # 前台页面 (学生端)
│   │   │   └── manager/           # 后台页面 (管理端)
│   │   ├── components/            # 通用组件
│   │   ├── router/                # 路由配置
│   │   ├── utils/                 # 工具函数
│   │   └── assets/                # 静态资源
│   ├── package.json               # 前端依赖
│   └── vue.config.js              # Vue配置
└── README.md                      # 项目说明文档
```

## 🚀 快速开始

### 📋 环境需求

#### 后端环境
- **JDK**: 17+
- **Maven**: 3.6+
- **MySQL**: 8.0+
- **Redis**: 6.0+

#### 前端环境
- **Node.js**: 16+
- **npm**: 8+ 或 yarn 1.22+

### ⚙️ 配置说明

#### 后端配置
项目使用多环境配置，支持开发、测试、生产环境：

- `application.yml` - 主配置文件
- `application-dev.yml` - 开发环境配置
- `logback-spring.xml` - 日志配置

#### 前端配置
- `vue.config.js` - Vue项目配置
- `.env.development` - 开发环境变量
- `.env.production` - 生产环境变量

### 🛠️ 部署步骤

#### 1. 克隆项目
```bash
git clone https://github.com/hqlzz/TCM.git
cd tcm-manager
```

#### 2. 后端部署

```bash
# 进入后端目录
cd springboot

# 导入数据库 (请先创建数据库)
mysql -u用户名 -p密码 数据库名 < manager.sql

# 修改配置文件
# 编辑 src/main/resources/application-dev.yml
# 配置数据库连接、Redis连接、阿里云OSS等

# 编译项目
mvn clean package -DskipTests

# 运行项目
java -jar target/springboot-0.0.1-SNAPSHOT.jar
```

#### 3. 前端部署

```bash
# 进入前端目录
cd vue

# 安装依赖
npm install
# 或使用 yarn install

# 开发模式运行
npm run serve
# 或 yarn serve

# 生产环境构建
npm run build
# 或 yarn build
```


## ⚡ 性能优化亮点

### 🚀 多级缓存架构
- **L1缓存**: Caffeine本地缓存，毫秒级响应
- **L2缓存**: Redis分布式缓存，支持集群
- **智能降级**: 缓存失效时自动降级到数据库
- **缓存预热**: 系统启动时预加载热点数据

### 🔧 数据库优化
- **HikariCP连接池**: 业界最快的数据库连接池
- **批量操作优化**: 事务批处理，减少数据库交互
- **查询优化**: N+1查询解决方案，预加载关联数据
- **索引优化**: 针对查询模式优化的数据库索引


### 📊 性能监控系统
- **实时监控**: 方法级性能追踪
- **智能告警**: 自动阈值检查和性能告警
- **资源监控**: 内存、线程、GC状态监控
- **缓存监控**: 缓存命中率和性能统计


## 🎨 系统特色

### 🔐 安全特性
- **JWT认证**: 无状态的用户认证机制
- **权限控制**: 基于角色的访问控制(RBAC)
- **数据加密**: 敏感数据加密存储
- **防护机制**: SQL注入、XSS攻击防护

### 🌍 多环境支持
- **开发环境**: 详细日志，开发工具集成
- **测试环境**: 模拟生产环境配置
- **生产环境**: 性能优化，安全加固

### 📱 响应式设计
- **移动端适配**: 支持手机、平板访问
- **跨浏览器兼容**: 支持主流浏览器
- **PWA支持**: 渐进式Web应用特性

### 🔄 数据同步
- **实时同步**: Redis缓存与数据库同步
- **数据一致性**: 分布式事务保证
- **备份恢复**: 自动数据备份机制


## 👥 开发团队

- **项目负责人**: [@hqlzz](https://github.com/hqlzz)
- **技术架构**: Spring Boot + Vue + MySQL + Redis
- **性能优化**: 多级缓存 + 异步处理 + 线程池优化


**⭐ 如果这个项目对您有帮助，请给我们一个Star！**



## 联系方式

如有问题或建议，请联系：<EMAIL>
