# 中医知识在线学习与测评系统 - 前端

![版本](https://img.shields.io/badge/版本-1.0.0-blue)
![Vue](https://img.shields.io/badge/Vue-2.x-brightgreen)
![Element Plus](https://img.shields.io/badge/Element%20Plus-Latest-blue)

中医知识在线学习与测评系统是一个专注于中医领域的教育平台前端，基于Vue 3开发，使用Element Plus组件库，提供美观易用的用户界面，让中医学习者能够便捷地进行知识学习和能力测评。

## 技术栈

- **核心框架**: Vue 2.x
- **UI库**: Element Plus
- **路由管理**: Vue Router
- **HTTP请求**: Axios
- **CSS预处理**: SCSS
- **图表库**: ECharts (用于学习进度和测评结果可视化)

## 项目结构

```
vue/
├── public/               # 静态资源
├── src/
│   ├── assets/           # 资源文件
│   │   ├── banner/       # 轮播图资源
│   │   ├── css/          # 全局样式
│   │   ├── imgs/         # 图片资源 (包含中医图谱、穴位图等)
│   │   └── styles/       # 自定义样式
│   ├── components/       # 通用组件
│   ├── router/           # 路由配置
│   ├── utils/            # 工具函数
│   ├── views/            # 页面组件
│   │   ├── front/        # 前台页面
│   │   └── manager/      # 管理端页面
│   ├── App.vue           # 根组件
│   └── main.js           # 入口文件
├── .eslintrc.js          # ESLint配置
├── vite.config.js        # Vite配置
└── package.json          # 项目依赖
```

## 主要功能模块

- **中医知识学习**:
  - 中医基础理论
  - 中药学与方剂学
  - 经络穴位学习
  - 中医诊断方法
  - 临床实践案例
  
- **能力测评系统**:
  - 理论知识测试
  - 中药识别测试
  - 方剂配伍测试
  - 经络穴位定位测试
  - 辨证论治能力测评
  
- **学习进度分析**:
  - 知识掌握度雷达图
  - 学习时长统计
  - 薄弱环节提示
  
- **学习社区**:
  - 问题讨论
  - 经验分享
  - 学习心得交流

## 快速开始

### 环境需求

- Node.js 16+
- npm 8+ 或 yarn 1.22+

### 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 开发模式

```bash
# 使用npm
npm run dev

# 或使用yarn
yarn dev
```

### 构建生产版本

```bash
# 使用npm
npm run build

# 或使用yarn
yarn build
```

### 环境变量配置

创建 `.env.development` 和 `.env.production` 文件来配置不同环境下的变量：

```
# .env.development
VITE_API_BASE_URL=http://localhost:9090

# .env.production
VITE_API_BASE_URL=https://api.your-domain.com
```

## 页面预览

### 首页展示

系统首页采用传统中医元素设计，展示中医基础理论、中药学、方剂学等核心模块入口，同时展示学习进度和推荐学习内容。

### 中医知识学习

提供丰富的中医知识学习内容，包括文字、图片和视频资料，涵盖中医基础理论、诊断方法、治疗原则等各方面知识。

### 经络穴位学习

通过互动式3D人体模型，帮助学习者直观了解经络走向和穴位位置，支持360度旋转查看和穴位定位练习。

### 中药识别学习

展示常用中药材的详细图片、性味归经、功效主治等信息，并提供识别训练功能。

### 测评系统

多样化的测评方式，包括选择题、判断题、中药图片识别、经络穴位定位等，全面检验学习成果。

### 学习进度分析

通过数据可视化展示学习进度、测评得分、知识掌握情况，帮助学习者了解自己的学习状态和需要加强的领域。

## 路由说明

项目分为前台和管理后台两大部分：

```
/                        # 前台首页
/course                  # 课程学习
/tcm                     # 中医知识库
/tcm/basic               # 中医基础理论
/tcm/herbs               # 中药学
/tcm/formulas            # 方剂学
/tcm/meridians           # 经络穴位
/tcm/diagnostics         # 中医诊断学
/exam                    # 知识测评
/community               # 学习社区
/user                    # 用户中心

/admin                   # 管理后台
/admin/dashboard         # 数据大盘
/admin/course            # 课程管理
/admin/tcm               # 中医知识管理
/admin/exam              # 测评管理
/admin/user              # 用户管理
/admin/system            # 系统配置
```

## 特色功能

- **中医经络3D交互**：使用Three.js实现3D人体模型，可旋转查看并标注经络走向和穴位位置
- **中药识别训练**：通过图像识别和测验强化中药材的识别能力
- **脉象模拟学习**：模拟不同脉象特点，提供视觉和触觉反馈
- **舌诊图谱比对**：上传舌相照片，与标准舌诊图谱比对分析
- **方剂组成训练**：通过交互式界面训练方剂的组成和配比知识

## 设计风格

系统采用中医传统元素与现代UI相结合的设计风格：

- 色彩方案采用中国传统色系，主色调为棕、米、黄等暖色系
- 布局参考古籍排版，结合现代响应式设计
- 图标设计融入中医元素，如方剂、针灸等符号

## 开发规范

- 组件名使用PascalCase命名
- 文件名使用kebab-case命名
- CSS类名使用BEM命名规范
- 代码提交前请执行lint检查和格式化

## 联系方式

如有问题或建议，请联系：<EMAIL>