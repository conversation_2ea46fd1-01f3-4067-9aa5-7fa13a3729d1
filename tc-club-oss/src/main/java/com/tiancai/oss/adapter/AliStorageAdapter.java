package com.tiancai.oss.adapter;

import com.tiancai.oss.entity.FileInfo;
import com.tiancai.oss.util.OssUtil;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.List;

/**
 * 阿里云OSS适配器
 */
public class AliStorageAdapter implements StorageAdapter {

    @Resource
    private OssUtil ossUtil;

    @Value("${aliyun.oss.bucket}")
    private String defaultBucketName;

    @Override
    @SneakyThrows
    public void createBucket(String bucket) {
        ossUtil.createBucket(bucket);
    }

    @Override
    @SneakyThrows
    public void uploadFile(MultipartFile uploadFile, String bucket, String objectName) {
        String bucketName = (bucket == null || bucket.isEmpty()) ? defaultBucketName : bucket;
        ossUtil.createBucket(bucketName);
        String objectKey = (objectName != null) ? objectName + "/" + uploadFile.getOriginalFilename() : uploadFile.getOriginalFilename();
        ossUtil.uploadFile(uploadFile.getInputStream(), bucketName, objectKey);
    }

    @Override
    @SneakyThrows
    public List<String> getAllBucket() {
        return ossUtil.getAllBuckets();
    }

    @Override
    @SneakyThrows
    public List<FileInfo> getAllFile(String bucket) {
        return ossUtil.getAllFiles(bucket);
    }

    @Override
    @SneakyThrows
    public InputStream downLoad(String bucket, String objectName) {
        return ossUtil.downloadFile(bucket, objectName);
    }

    @Override
    @SneakyThrows
    public void deleteBucket(String bucket) {
        ossUtil.deleteBucket(bucket);
    }

    @Override
    @SneakyThrows
    public void deleteObject(String bucket, String objectName) {
        ossUtil.deleteFile(bucket, objectName);
    }

    @Override
    @SneakyThrows
    public String getUrl(String bucket, String objectName) {
        String bucketName = (bucket == null || bucket.isEmpty()) ? defaultBucketName : bucket;
        return ossUtil.getPreviewFileUrl(bucketName, objectName);
    }
}
