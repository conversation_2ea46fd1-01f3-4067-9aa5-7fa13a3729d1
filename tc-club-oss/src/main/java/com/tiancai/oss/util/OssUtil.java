package com.tiancai.oss.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.*;
import com.tiancai.oss.entity.FileInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.InputStream;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 阿里云OSS文件操作工具
 */
@Component
public class OssUtil {

    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    @Value("${aliyun.oss.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.oss.secret}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucket}")
    private String defaultBucketName;

    private OSS ossClient;

    @PostConstruct
    public void init() {
        ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }

    /**
     * 创建bucket
     */
    public void createBucket(String bucketName) {
        if (!ossClient.doesBucketExist(bucketName)) {
            ossClient.createBucket(bucketName);
        }
    }

    /**
     * 上传文件
     */
    public void uploadFile(InputStream inputStream, String bucketName, String objectName) {
        ossClient.putObject(bucketName, objectName, inputStream);
    }

    /**
     * 列出所有bucket
     */
    public List<String> getAllBuckets() {
        List<Bucket> buckets = ossClient.listBuckets();
        return buckets.stream().map(Bucket::getName).collect(Collectors.toList());
    }

    /**
     * 列出当前bucket及文件
     */
    public List<FileInfo> getAllFiles(String bucketName) {
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName);
        ObjectListing objectListing = ossClient.listObjects(listObjectsRequest);
        List<FileInfo> fileInfoList = new LinkedList<>();
        for (OSSObjectSummary ossObjectSummary : objectListing.getObjectSummaries()) {
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileName(ossObjectSummary.getKey());
            fileInfo.setDirectoryFlag(ossObjectSummary.getKey().endsWith("/"));
            fileInfo.setEtag(ossObjectSummary.getETag());
            fileInfoList.add(fileInfo);
        }
        return fileInfoList;
    }

    /**
     * 下载文件
     */
    public InputStream downloadFile(String bucketName, String objectName) {
        OSSObject ossObject = ossClient.getObject(bucketName, objectName);
        return ossObject.getObjectContent();
    }

    /**
     * 删除bucket
     */
    public void deleteBucket(String bucketName) {
        ossClient.deleteBucket(bucketName);
    }

    /**
     * 删除文件
     */
    public void deleteFile(String bucketName, String objectName) {
        ossClient.deleteObject(bucketName, objectName);
    }

    /**
     * 获取文件url
     */
    public String getPreviewFileUrl(String bucketName, String objectName) {
        Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000); // 1 hour
        return ossClient.generatePresignedUrl(bucketName, objectName, expiration).toString();
    }

    @PreDestroy
    public void destroy() {
        if (ossClient != null) {
            ossClient.shutdown();
        }
    }
}
