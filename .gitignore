# ================================
# Spring Boot + Sa-Token 项目 .gitignore
# ================================

# ================================
# Java 编译产物
# ================================
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# ================================
# Maven 构建产物
# ================================
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.properties

# ================================
# Gradle 构建产物（如果使用）
# ================================
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# ================================
# IDE 相关文件
# ================================

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# VS Code
.vscode/
*.code-workspace

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# ================================
# 日志文件
# ================================
logs/
*.log
*.log.*
log/
*.out

# Spring Boot 日志
spring.log
application.log

# ================================
# Spring Boot 特定文件
# ================================
# Spring Boot DevTools
.spring-boot-devtools.properties

# Spring Boot 配置文件（敏感信息）
# 注意：如果有包含敏感信息的配置文件，取消下面的注释
# application-prod.yml
# application-prod.properties
# application-secret.yml
# application-secret.properties

# ================================
# 数据库文件
# ================================
# H2 数据库文件
*.db
*.mv.db
*.trace.db
*.lock.db

# SQLite
*.sqlite
*.sqlite3

# Derby
derby.log

# ================================
# 缓存和临时文件
# ================================
# Redis dump
dump.rdb

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 缓存目录
.cache/
cache/

# ================================
# 操作系统相关文件
# ================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ================================
# 开发工具和插件
# ================================
# JRebel
rebel.xml

# JProfiler
*.jprofiler

# YourKit
*.hprof

# JMeter
*.jmx.bak

# ================================
# 文档和报告
# ================================
# Maven site
site/

# Surefire reports
surefire-reports/

# TestNG reports
test-output/

# SpotBugs
.spotbugs

# PMD
.pmd

# Checkstyle
.checkstyle

# ================================
# 安全和密钥文件
# ================================
# 私钥文件
*.pem
*.key
*.p12
*.jks
*.keystore

# 环境变量文件
.env
.env.local
.env.*.local

# ================================
# 上传文件目录
# ================================
# 文件上传目录（根据项目实际情况调整）
uploads/
files/
static/uploads/

# ================================
# 其他项目特定文件
# ================================
# 备份文件
*.bak
*.backup

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.tar

# 包管理器
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ================================
# 自定义忽略规则
# ================================
# 在这里添加项目特定的忽略规则

# 示例：忽略特定的配置文件
# config/database.yml
# config/secrets.yml

# 示例：忽略生成的文档
# docs/generated/

# 示例：忽略测试数据
# test-data/
