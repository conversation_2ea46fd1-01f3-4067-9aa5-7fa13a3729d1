{"name": "vue", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"axios": "^1.5.1", "core-js": "^3.8.3", "echarts": "^5.6.0", "element-ui": "^2.15.14", "three": "^0.163.0", "vue": "^2.6.14", "vue-3d-carousel": "^1.0.5", "vue-awesome-swiper": "^3.1.3", "vue-router": "^3.5.1", "vuex": "^3.6.2", "wangeditor": "^4.7.15"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "~5.0.0", "sass": "^1.88.0", "sass-loader": "^16.0.5", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}